import React, { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardH<PERSON>er, CardTitle } from './ui/card';
import { Progress } from './ui/progress';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
// @ts-ignore
import { generateStoryIllustrations } from '../services/illustrationGenerator';
import storyData from '../data/storyData';

interface GenerationProgress {
  current: number;
  total: number;
  pageId: number;
  status: 'generating' | 'completed' | 'error';
  imageUrl?: string;
  error?: string;
}

interface Illustration {
  pageId: number;
  imageUrl?: string;
  error?: string;
  generatedAt: string;
  prompt?: string;
}

export function IllustrationGenerator() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState<GenerationProgress | null>(null);
  const [illustrations, setIllustrations] = useState<Illustration[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [completed, setCompleted] = useState(false);

  const nonInteractivePages = storyData.pages.filter(page => !page.isInteractive);

  const handleGenerateIllustrations = async () => {
    setIsGenerating(true);
    setError(null);
    setIllustrations([]);
    setCompleted(false);
    setProgress(null);

    try {
      const results = await generateStoryIllustrations(
        storyData.pages,
        (progressData: GenerationProgress) => {
          setProgress(progressData);
        }
      );

      setIllustrations(results);
      setCompleted(true);
      console.log('所有插画生成完成:', results);
    } catch (err) {
      console.error('批量生成插画失败:', err);
      setError(`生成失败: ${(err as Error).message}`);
    } finally {
      setIsGenerating(false);
      setProgress(null);
    }
  };

  const downloadIllustration = async (imageUrl: string, pageId: number) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `page-${pageId}-illustration.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('下载失败:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'generating': return 'bg-blue-500';
      case 'completed': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-300';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'generating': return '生成中...';
      case 'completed': return '已完成';
      case 'error': return '生成失败';
      default: return '等待中';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl text-center">
          🎨 绘本插画批量生成器
        </CardTitle>
        <p className="text-center text-gray-600">
          为《小熊波波的友谊冒险》生成完整的插画集
        </p>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-6">
          {/* 生成控制区域 */}
          <div className="text-center">
            <p className="mb-4">
              将为 <Badge variant="secondary">{nonInteractivePages.length}</Badge> 个非交互页面生成插画
            </p>
            
            <Button 
              onClick={handleGenerateIllustrations}
              disabled={isGenerating}
              size="lg"
              className="w-full sm:w-auto"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  生成中...
                </>
              ) : (
                <>
                  🚀 开始生成插画
                </>
              )}
            </Button>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 进度显示 */}
          {progress && (
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">
                  正在生成页面 {progress.pageId} 的插画...
                </span>
                <span className="text-sm text-gray-500">
                  {progress.current} / {progress.total}
                </span>
              </div>
              
              <Progress 
                value={(progress.current / progress.total) * 100} 
                className="h-2"
              />
              
              <div className="text-center text-sm text-gray-600">
                {getStatusText(progress.status)}
              </div>
            </div>
          )}

          {/* 页面状态列表 */}
          {(isGenerating || completed) && (
            <div className="space-y-2">
              <h3 className="font-semibold">生成进度：</h3>
              <div className="grid gap-2">
                {nonInteractivePages.map((page) => {
                  const illustration = illustrations.find(ill => ill.pageId === page.id);
                  const isCurrentPage = progress?.pageId === page.id;
                  const status = illustration?.error ? 'error' : 
                                illustration?.imageUrl ? 'completed' : 
                                isCurrentPage ? 'generating' : 'waiting';
                  
                  return (
                    <div 
                      key={page.id}
                      className={`flex items-center justify-between p-3 rounded-lg border ${
                        isCurrentPage ? 'border-blue-300 bg-blue-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${getStatusColor(status)}`}></div>
                        <span className="font-medium">页面 {page.id}</span>
                        <span className="text-sm text-gray-600 truncate max-w-md">
                          {page.content.substring(0, 50)}...
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Badge variant={status === 'completed' ? 'default' : 'secondary'}>
                          {getStatusText(status)}
                        </Badge>
                        
                        {illustration?.imageUrl && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => downloadIllustration(illustration.imageUrl!, page.id)}
                          >
                            下载
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* 完成总结 */}
          {completed && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-semibold text-green-800 mb-2">生成完成！</h3>
              <p className="text-green-700">
                成功生成了 {illustrations.filter(ill => ill.imageUrl).length} 张插画，
                {illustrations.filter(ill => ill.error).length > 0 && 
                  ` ${illustrations.filter(ill => ill.error).length} 张生成失败`
                }
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
