// 绘本插画自动生成和管理服务
import { getApiKey } from './apiKeyManager.js';
import storyData from '../data/storyData.ts';

/**
 * 统一的风格描述（确保所有插画风格一致）
 */
function getUnifiedStyleDescription() {
  return `
创建一幅温暖友好的儿童绘本插画，专为6-8岁自闭症儿童设计。

风格要求：
- 温暖柔和的水彩风格
- 柔和的色彩搭配：森林绿、温暖棕、天空蓝、花朵粉
- 简洁清晰的线条，避免过多细节
- 圆润可爱的造型设计
- 表情温和友善，营造安全感
- 背景简洁，主体突出

核心角色设计（必须保持一致）：
- 小熊波波：中等身材的棕色小熊，圆润的身形，友善的黑色小眼睛，小小的黑鼻子，穿着简单的红色背心
- 小兔子莉莉：雪白毛发的小兔子，粉红色鼻子，长长的耳朵，穿着淡蓝色小裙子
- 猫头鹰：棕色羽毛，大大的圆眼睛，智慧的表情
- 松鼠兄弟：灰色毛发，蓬松的尾巴，活泼的表情
- 乌龟：绿色的壳，慈祥温和的表情

环境设定：
- 森林场景：绿色为主调，有高大的橡树、松树
- 草地：嫩绿色，点缀着小野花
- 小木屋：温馨的棕色木屋，红色屋顶
- 光线：柔和的自然光，营造温馨氛围`;
}

/**
 * 为每个页面构建专门的提示词
 */
function buildPagePrompt(page) {
  const baseStyle = getUnifiedStyleDescription();
  
  // 根据页面内容生成具体场景描述
  let sceneDescription = '';
  
  switch (page.id) {
    case 1:
      sceneDescription = `
场景：小熊波波坐在自己的小木屋前
- 波波独自坐在木屋门前的台阶上
- 背景是美丽的森林，远处有大树和野花
- 波波的表情略显孤独但充满好奇
- 早晨的阳光透过树叶洒下斑驳的光影
- 整体氛围宁静而温馨`;
      break;
      
    case 2:
      sceneDescription = `
场景：波波第一次离开小木屋，沿着小路探索
- 波波正在森林小径上行走，表情好奇而略显紧张
- 小路蜿蜒通向远方，两旁是绿色的树木和灌木
- 可以看到远处传来歌声的方向有微弱的光亮
- 波波的姿态显示出他的勇气和决心
- 森林环境生机勃勃，充满探索的魅力`;
      break;
      
    case 3:
      sceneDescription = `
场景：波波发现小兔子莉莉在草地上采花
- 开阔的绿色草地，点缀着各色野花
- 莉莉正在采摘花朵，表情愉快，似乎在哼歌
- 波波在远处观察，表情好奇而羞涩
- 阳光明媚，整个场景充满生机和活力
- 花朵种类丰富：雏菊、蒲公英、小野花`;
      break;
      
    case 5:
      sceneDescription = `
场景：波波和莉莉初次相遇，开始一起采花
- 波波和莉莉并肩站在花丛中
- 两人都在采摘花朵，莉莉在教波波如何选择最美的花
- 表情都很友善和快乐，显示出友谊的开始
- 周围散落着已采摘的花朵
- 背景是美丽的草地和远山`;
      break;
      
    case 6:
      sceneDescription = `
场景：波波和莉莉一起制作花环，深入交谈
- 两人坐在草地上，手中拿着花朵和半成品花环
- 莉莉正在向波波介绍其他森林朋友
- 表情轻松愉快，显示出友谊的加深
- 周围有制作花环的材料和工具
- 背景可以隐约看到大橡树的轮廓`;
      break;
      
    case 7:
      sceneDescription = `
场景：波波对参加野餐会既兴奋又紧张
- 波波的表情复杂：既有期待也有担忧
- 莉莉在一旁鼓励他，表情温暖支持
- 背景可以看到通往大橡树的小路
- 夕阳西下，为场景增添温暖的色调
- 整体氛围体现出友谊的支持和鼓励`;
      break;
      
    case 9:
      sceneDescription = `
场景：野餐会上大家分享美食
- 大橡树下铺着野餐布，上面摆满各种食物
- 猫头鹰、松鼠兄弟、乌龟、莉莉围坐在一起
- 波波站在一旁，表情略显不安
- 食物丰富：蜂蜜饼干、坚果沙拉、新鲜浆果
- 温暖的下午阳光透过树叶洒下`;
      break;
      
    case 10:
      sceneDescription = `
场景：莉莉安慰波波，传达友谊的真谛
- 莉莉温柔地对波波说话，手轻抚波波的肩膀
- 波波的表情从担忧转为感动和理解
- 其他动物朋友在背景中友善地看着他们
- 整个场景充满温暖和关爱的氛围
- 夕阳的光线为场景增添温馨感`;
      break;
      
    case 12:
      sceneDescription = `
场景：波波快乐地与所有朋友在一起，展现友谊的美好
- 波波站在朋友们中间，表情自信快乐
- 所有角色都在场：莉莉、猫头鹰、松鼠兄弟、乌龟
- 大家围成一个温馨的圆圈，表情都很开心
- 背景是美丽的森林全景，象征着波波的新世界
- 整体氛围欢乐祥和，体现友谊的力量`;
      break;
      
    default:
      sceneDescription = `根据故事内容：${page.content}`;
  }

  return `${baseStyle}

${sceneDescription}

故事内容：${page.content}

请确保：
1. 角色外观与之前描述完全一致
2. 色彩风格统一协调
3. 构图简洁明了，适合儿童理解
4. 表情和动作符合故事情节
5. 整体氛围温暖安全，适合自闭症儿童`;
}

/**
 * 调用DALL-E 3 API生成图像
 */
async function generateImage(prompt) {
  const apiKey = getApiKey();
  if (!apiKey) {
    throw new Error('API密钥未设置');
  }

  try {
    const requestBody = {
      model: "dall-e-3",
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      quality: "standard",
      style: "natural"
    };

    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API错误: ${errorData.error?.message || '未知错误'}`);
    }

    const data = await response.json();
    return data.data[0].url;
  } catch (error) {
    console.error('图像生成失败:', error);
    throw error;
  }
}

/**
 * 下载图像并保存到本地
 */
async function downloadAndSaveImage(imageUrl, filename) {
  try {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    return filename;
  } catch (error) {
    console.error('下载图像失败:', error);
    throw error;
  }
}

/**
 * 生成完整的绘本插画集
 */
export async function generateCompleteIllustrationSet() {
  console.log('开始生成《小熊波波的友谊冒险》完整插画集...');
  
  const nonInteractivePages = storyData.pages.filter(page => !page.isInteractive);
  const illustrations = [];
  const errors = [];
  
  for (let i = 0; i < nonInteractivePages.length; i++) {
    const page = nonInteractivePages[i];
    
    try {
      console.log(`正在生成页面 ${page.id} 的插画... (${i + 1}/${nonInteractivePages.length})`);
      
      // 构建提示词
      const prompt = buildPagePrompt(page);
      
      // 生成图像
      const imageUrl = await generateImage(prompt);
      
      // 下载并保存图像
      const filename = `page-${page.id}-illustration.png`;
      await downloadAndSaveImage(imageUrl, filename);
      
      // 记录成功的插画
      const illustration = {
        pageId: page.id,
        imageUrl: imageUrl,
        filename: filename,
        prompt: prompt,
        generatedAt: new Date().toISOString()
      };
      
      illustrations.push(illustration);
      console.log(`✅ 页面 ${page.id} 插画生成成功`);
      
      // 添加延迟避免API限制
      if (i < nonInteractivePages.length - 1) {
        console.log('等待2秒后继续...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } catch (error) {
      console.error(`❌ 页面 ${page.id} 插画生成失败:`, error);
      errors.push({
        pageId: page.id,
        error: error.message,
        generatedAt: new Date().toISOString()
      });
    }
  }
  
  // 生成总结报告
  const report = {
    totalPages: nonInteractivePages.length,
    successCount: illustrations.length,
    errorCount: errors.length,
    illustrations: illustrations,
    errors: errors,
    generatedAt: new Date().toISOString()
  };
  
  console.log('🎨 插画生成完成！');
  console.log(`✅ 成功生成: ${report.successCount} 张`);
  console.log(`❌ 生成失败: ${report.errorCount} 张`);
  
  return report;
}

/**
 * 获取页面对应的插画URL（如果已生成）
 */
export function getPageIllustration(pageId) {
  // 这里可以从localStorage或其他存储中获取已生成的插画
  const storedIllustrations = localStorage.getItem('storyIllustrations');
  if (storedIllustrations) {
    const illustrations = JSON.parse(storedIllustrations);
    const illustration = illustrations.find(ill => ill.pageId === pageId);
    return illustration?.imageUrl || null;
  }
  return null;
}

/**
 * 保存插画信息到本地存储
 */
export function saveIllustrationsToStorage(illustrations) {
  localStorage.setItem('storyIllustrations', JSON.stringify(illustrations));
  console.log('插画信息已保存到本地存储');
}

export default {
  generateCompleteIllustrationSet,
  getPageIllustration,
  saveIllustrationsToStorage,
  getUnifiedStyleDescription
};
