{"version": 3, "sources": ["../../tailwind-merge/src/lib/tw-join.ts", "../../tailwind-merge/src/lib/class-utils.ts", "../../tailwind-merge/src/lib/lru-cache.ts", "../../tailwind-merge/src/lib/modifier-utils.ts", "../../tailwind-merge/src/lib/config-utils.ts", "../../tailwind-merge/src/lib/merge-classlist.ts", "../../tailwind-merge/src/lib/create-tailwind-merge.ts", "../../tailwind-merge/src/lib/from-theme.ts", "../../tailwind-merge/src/lib/validators.ts", "../../tailwind-merge/src/lib/default-config.ts", "../../tailwind-merge/src/lib/merge-configs.ts", "../../tailwind-merge/src/lib/extend-tailwind-merge.ts", "../../tailwind-merge/src/lib/tw-merge.ts", "../../tailwind-merge/src/index.ts"], "sourcesContent": ["/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\n\nexport type ClassNameValue = ClassNameArray | string | null | undefined | 0 | false\ntype ClassNameArray = ClassNameValue[]\n\nexport function twJoin(...classLists: ClassNameValue[]): string\nexport function twJoin() {\n    let index = 0\n    let argument: ClassNameValue\n    let resolvedValue: string\n    let string = ''\n\n    while (index < arguments.length) {\n        if ((argument = arguments[index++])) {\n            if ((resolvedValue = toValue(argument))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n    return string\n}\n\nfunction toValue(mix: ClassNameArray | string) {\n    if (typeof mix === 'string') {\n        return mix\n    }\n\n    let resolvedValue: string\n    let string = ''\n\n    for (let k = 0; k < mix.length; k++) {\n        if (mix[k]) {\n            if ((resolvedValue = toValue(mix[k] as ClassNameArray | string))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n\n    return string\n}\n", "import { ClassGroup, ClassGroupId, ClassValidator, Config, ThemeGetter, ThemeObject } from './types'\n\nexport interface ClassPartObject {\n    nextPart: Map<string, ClassPartObject>\n    validators: ClassValidatorObject[]\n    classGroupId?: ClassGroupId\n}\n\ninterface ClassValidatorObject {\n    classGroupId: ClassGroupId\n    validator: ClassValidator\n}\n\nconst CLASS_PART_SEPARATOR = '-'\n\nexport function createClassUtils(config: Config) {\n    const classMap = createClassMap(config)\n    const { conflictingClassGroups, conflictingClassGroupModifiers = {} } = config\n\n    function getClassGroupId(className: string) {\n        const classParts = className.split(CLASS_PART_SEPARATOR)\n\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift()\n        }\n\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className)\n    }\n\n    function getConflictingClassGroupIds(classGroupId: ClassGroupId, hasPostfixModifier: boolean) {\n        const conflicts = conflictingClassGroups[classGroupId] || []\n\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]!]\n        }\n\n        return conflicts\n    }\n\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds,\n    }\n}\n\nfunction getGroupRecursive(\n    classParts: string[],\n    classPartObject: ClassPartObject,\n): ClassGroupId | undefined {\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId\n    }\n\n    const currentClassPart = classParts[0]!\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart)\n    const classGroupFromNextClassPart = nextClassPartObject\n        ? getGroupRecursive(classParts.slice(1), nextClassPartObject)\n        : undefined\n\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart\n    }\n\n    if (classPartObject.validators.length === 0) {\n        return undefined\n    }\n\n    const classRest = classParts.join(CLASS_PART_SEPARATOR)\n\n    return classPartObject.validators.find(({ validator }) => validator(classRest))?.classGroupId\n}\n\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/\n\nfunction getGroupIdForArbitraryProperty(className: string) {\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)![1]\n        const property = arbitraryPropertyClassName?.substring(\n            0,\n            arbitraryPropertyClassName.indexOf(':'),\n        )\n\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property\n        }\n    }\n}\n\n/**\n * Exported for testing only\n */\nexport function createClassMap(config: Config) {\n    const { theme, prefix } = config\n    const classMap: ClassPartObject = {\n        nextPart: new Map<string, ClassPartObject>(),\n        validators: [],\n    }\n\n    const prefixedClassGroupEntries = getPrefixedClassGroupEntries(\n        Object.entries(config.classGroups),\n        prefix,\n    )\n\n    prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n        processClassesRecursively(classGroup, classMap, classGroupId, theme)\n    })\n\n    return classMap\n}\n\nfunction processClassesRecursively(\n    classGroup: ClassGroup,\n    classPartObject: ClassPartObject,\n    classGroupId: ClassGroupId,\n    theme: ThemeObject,\n) {\n    classGroup.forEach((classDefinition) => {\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit =\n                classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition)\n            classPartObjectToEdit.classGroupId = classGroupId\n            return\n        }\n\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(\n                    classDefinition(theme),\n                    classPartObject,\n                    classGroupId,\n                    theme,\n                )\n                return\n            }\n\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId,\n            })\n\n            return\n        }\n\n        Object.entries(classDefinition).forEach(([key, classGroup]) => {\n            processClassesRecursively(\n                classGroup,\n                getPart(classPartObject, key),\n                classGroupId,\n                theme,\n            )\n        })\n    })\n}\n\nfunction getPart(classPartObject: ClassPartObject, path: string) {\n    let currentClassPartObject = classPartObject\n\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart) => {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: [],\n            })\n        }\n\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart)!\n    })\n\n    return currentClassPartObject\n}\n\nfunction isThemeGetter(func: ClassValidator | ThemeGetter): func is ThemeGetter {\n    return (func as ThemeGetter).isThemeGetter\n}\n\nfunction getPrefixedClassGroupEntries(\n    classGroupEntries: Array<[classGroupId: string, classGroup: ClassGroup]>,\n    prefix: string | undefined,\n): Array<[classGroupId: string, classGroup: ClassGroup]> {\n    if (!prefix) {\n        return classGroupEntries\n    }\n\n    return classGroupEntries.map(([classGroupId, classGroup]) => {\n        const prefixedClassGroup = classGroup.map((classDefinition) => {\n            if (typeof classDefinition === 'string') {\n                return prefix + classDefinition\n            }\n\n            if (typeof classDefinition === 'object') {\n                return Object.fromEntries(\n                    Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]),\n                )\n            }\n\n            return classDefinition\n        })\n\n        return [classGroupId, prefixedClassGroup]\n    })\n}\n", "// Export is needed because TypeScript complains about an error otherwise:\n// Error: …/tailwind-merge/src/config-utils.ts(8,17): semantic error TS4058: Return type of exported function has or is using name 'LruCache' from external module \"…/tailwind-merge/src/lru-cache\" but cannot be named.\nexport interface LruCache<Key, Value> {\n    get(key: Key): Value | undefined\n    set(key: Key, value: Value): void\n}\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nexport function createLruCache<Key, Value>(maxCacheSize: number): LruCache<Key, Value> {\n    if (maxCacheSize < 1) {\n        return {\n            get: () => undefined,\n            set: () => {},\n        }\n    }\n\n    let cacheSize = 0\n    let cache = new Map<Key, Value>()\n    let previousCache = new Map<Key, Value>()\n\n    function update(key: Key, value: Value) {\n        cache.set(key, value)\n        cacheSize++\n\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0\n            previousCache = cache\n            cache = new Map()\n        }\n    }\n\n    return {\n        get(key) {\n            let value = cache.get(key)\n\n            if (value !== undefined) {\n                return value\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value)\n                return value\n            }\n        },\n        set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value)\n            } else {\n                update(key, value)\n            }\n        },\n    }\n}\n", "import { Config } from './types'\n\nexport const IMPORTANT_MODIFIER = '!'\n\nexport function createSplitModifiers(config: Config) {\n    const separator = config.separator || ':'\n    const isSeparatorSingleCharacter = separator.length === 1\n    const firstSeparatorCharacter = separator[0]\n    const separatorLength = separator.length\n\n    // splitModifiers inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n    return function splitModifiers(className: string) {\n        const modifiers = []\n\n        let bracketDepth = 0\n        let modifierStart = 0\n        let postfixModifierPosition: number | undefined\n\n        for (let index = 0; index < className.length; index++) {\n            let currentCharacter = className[index]\n\n            if (bracketDepth === 0) {\n                if (\n                    currentCharacter === firstSeparatorCharacter &&\n                    (isSeparatorSingleCharacter ||\n                        className.slice(index, index + separatorLength) === separator)\n                ) {\n                    modifiers.push(className.slice(modifierStart, index))\n                    modifierStart = index + separatorLength\n                    continue\n                }\n\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index\n                    continue\n                }\n            }\n\n            if (currentCharacter === '[') {\n                bracketDepth++\n            } else if (currentCharacter === ']') {\n                bracketDepth--\n            }\n        }\n\n        const baseClassNameWithImportantModifier =\n            modifiers.length === 0 ? className : className.substring(modifierStart)\n        const hasImportantModifier =\n            baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER)\n        const baseClassName = hasImportantModifier\n            ? baseClassNameWithImportantModifier.substring(1)\n            : baseClassNameWithImportantModifier\n\n        const maybePostfixModifierPosition =\n            postfixModifierPosition && postfixModifierPosition > modifierStart\n                ? postfixModifierPosition - modifierStart\n                : undefined\n\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        }\n    }\n}\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nexport function sortModifiers(modifiers: string[]) {\n    if (modifiers.length <= 1) {\n        return modifiers\n    }\n\n    const sortedModifiers: string[] = []\n    let unsortedModifiers: string[] = []\n\n    modifiers.forEach((modifier) => {\n        const isArbitraryVariant = modifier[0] === '['\n\n        if (isArbitraryVariant) {\n            sortedModifiers.push(...unsortedModifiers.sort(), modifier)\n            unsortedModifiers = []\n        } else {\n            unsortedModifiers.push(modifier)\n        }\n    })\n\n    sortedModifiers.push(...unsortedModifiers.sort())\n\n    return sortedModifiers\n}\n", "import { createClassUtils } from './class-utils'\nimport { createLruCache } from './lru-cache'\nimport { createSplitModifiers } from './modifier-utils'\nimport { Config } from './types'\n\nexport type ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createConfigUtils(config: Config) {\n    return {\n        cache: createLruCache<string, string>(config.cacheSize),\n        splitModifiers: createSplitModifiers(config),\n        ...createClassUtils(config),\n    }\n}\n", "import { ConfigUtils } from './config-utils'\nimport { IMPORTANT_MODIFIER, sortModifiers } from './modifier-utils'\n\nconst SPLIT_CLASSES_REGEX = /\\s+/\n\nexport function mergeClassList(classList: string, configUtils: ConfigUtils) {\n    const { splitModifiers, getClassGroupId, getConflictingClassGroupIds } = configUtils\n\n    /**\n     * Set of classGroupIds in following format:\n     * `{importantModifier}{variantModifiers}{classGroupId}`\n     * @example 'float'\n     * @example 'hover:focus:bg-color'\n     * @example 'md:!pr'\n     */\n    const classGroupsInConflict = new Set<string>()\n\n    return (\n        classList\n            .trim()\n            .split(SPLIT_CLASSES_REGEX)\n            .map((originalClassName) => {\n                const {\n                    modifiers,\n                    hasImportantModifier,\n                    baseClassName,\n                    maybePostfixModifierPosition,\n                } = splitModifiers(originalClassName)\n\n                let classGroupId = getClassGroupId(\n                    maybePostfixModifierPosition\n                        ? baseClassName.substring(0, maybePostfixModifierPosition)\n                        : baseClassName,\n                )\n\n                let hasPostfixModifier = Boolean(maybePostfixModifierPosition)\n\n                if (!classGroupId) {\n                    if (!maybePostfixModifierPosition) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    classGroupId = getClassGroupId(baseClassName)\n\n                    if (!classGroupId) {\n                        return {\n                            isTailwindClass: false as const,\n                            originalClassName,\n                        }\n                    }\n\n                    hasPostfixModifier = false\n                }\n\n                const variantModifier = sortModifiers(modifiers).join(':')\n\n                const modifierId = hasImportantModifier\n                    ? variantModifier + IMPORTANT_MODIFIER\n                    : variantModifier\n\n                return {\n                    isTailwindClass: true as const,\n                    modifierId,\n                    classGroupId,\n                    originalClassName,\n                    hasPostfixModifier,\n                }\n            })\n            .reverse()\n            // Last class in conflict wins, so we need to filter conflicting classes in reverse order.\n            .filter((parsed) => {\n                if (!parsed.isTailwindClass) {\n                    return true\n                }\n\n                const { modifierId, classGroupId, hasPostfixModifier } = parsed\n\n                const classId = modifierId + classGroupId\n\n                if (classGroupsInConflict.has(classId)) {\n                    return false\n                }\n\n                classGroupsInConflict.add(classId)\n\n                getConflictingClassGroupIds(classGroupId, hasPostfixModifier).forEach((group) =>\n                    classGroupsInConflict.add(modifierId + group),\n                )\n\n                return true\n            })\n            .reverse()\n            .map((parsed) => parsed.originalClassName)\n            .join(' ')\n    )\n}\n", "import { createConfigUtils } from './config-utils'\nimport { mergeClassList } from './merge-classlist'\nimport { ClassNameValue, twJoin } from './tw-join'\nimport { Config } from './types'\n\ntype CreateConfigFirst = () => Config\ntype CreateConfigSubsequent = (config: Config) => Config\ntype TailwindMerge = (...classLists: ClassNameValue[]) => string\ntype ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createTailwindMerge(\n    ...createConfig: [CreateConfigFirst, ...CreateConfigSubsequent[]]\n): TailwindMerge {\n    let configUtils: ConfigUtils\n    let cacheGet: ConfigUtils['cache']['get']\n    let cacheSet: ConfigUtils['cache']['set']\n    let functionToCall = initTailwindMerge\n\n    function initTailwindMerge(classList: string) {\n        const [firstCreateConfig, ...restCreateConfig] = createConfig\n\n        const config = restCreateConfig.reduce(\n            (previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig),\n            firstCreateConfig(),\n        )\n\n        configUtils = createConfigUtils(config)\n        cacheGet = configUtils.cache.get\n        cacheSet = configUtils.cache.set\n        functionToCall = tailwindMerge\n\n        return tailwindMerge(classList)\n    }\n\n    function tailwindMerge(classList: string) {\n        const cachedResult = cacheGet(classList)\n\n        if (cachedResult) {\n            return cachedResult\n        }\n\n        const result = mergeClassList(classList, configUtils)\n        cacheSet(classList, result)\n\n        return result\n    }\n\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments as any))\n    }\n}\n", "import { ThemeGetter, ThemeObject } from './types'\n\nexport function fromTheme(key: string): ThemeGetter {\n    const themeGetter = (theme: ThemeObject) => theme[key] || []\n\n    themeGetter.isThemeGetter = true as const\n\n    return themeGetter\n}\n", "const arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i\nconst fractionRegex = /^\\d+\\/\\d+$/\nconst stringLengths = new Set(['px', 'full', 'screen'])\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/\nconst lengthUnitRegex =\n    /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/\n// Shadow always begins with x and y offset separated by underscore\nconst shadowRegex = /^-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/\n\nexport function isLength(value: string) {\n    return (\n        isNumber(value) ||\n        stringLengths.has(value) ||\n        fractionRegex.test(value) ||\n        isArbitraryLength(value)\n    )\n}\n\nexport function isArbitraryLength(value: string) {\n    return getIsArbitraryValue(value, 'length', isLengthOnly)\n}\n\nexport function isArbitrarySize(value: string) {\n    return getIsArbitraryValue(value, 'size', isNever)\n}\n\nexport function isArbitraryPosition(value: string) {\n    return getIsArbitraryValue(value, 'position', isNever)\n}\n\nexport function isArbitraryUrl(value: string) {\n    return getIsArbitraryValue(value, 'url', isUrl)\n}\n\nexport function isArbitraryNumber(value: string) {\n    return getIsArbitraryValue(value, 'number', isNumber)\n}\n\n/**\n * @deprecated Will be removed in next major version. Use `isArbitraryNumber` instead.\n */\nexport const isArbitraryWeight = isArbitraryNumber\n\nexport function isNumber(value: string) {\n    return !Number.isNaN(Number(value))\n}\n\nexport function isPercent(value: string) {\n    return value.endsWith('%') && isNumber(value.slice(0, -1))\n}\n\nexport function isInteger(value: string) {\n    return isIntegerOnly(value) || getIsArbitraryValue(value, 'number', isIntegerOnly)\n}\n\nexport function isArbitraryValue(value: string) {\n    return arbitraryValueRegex.test(value)\n}\n\nexport function isAny() {\n    return true\n}\n\nexport function isTshirtSize(value: string) {\n    return tshirtUnitRegex.test(value)\n}\n\nexport function isArbitraryShadow(value: string) {\n    return getIsArbitraryValue(value, '', isShadow)\n}\n\nfunction getIsArbitraryValue(value: string, label: string, testValue: (value: string) => boolean) {\n    const result = arbitraryValueRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return result[1] === label\n        }\n\n        return testValue(result[2]!)\n    }\n\n    return false\n}\n\nfunction isLengthOnly(value: string) {\n    return lengthUnitRegex.test(value)\n}\n\nfunction isNever() {\n    return false\n}\n\nfunction isUrl(value: string) {\n    return value.startsWith('url(')\n}\n\nfunction isIntegerOnly(value: string) {\n    return Number.isInteger(Number(value))\n}\n\nfunction isShadow(value: string) {\n    return shadowRegex.test(value)\n}\n", "import { fromTheme } from './from-theme'\nimport { Config } from './types'\nimport {\n    isAny,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryUrl,\n    isArbitraryValue,\n    isInteger,\n    isLength,\n    isNumber,\n    isPercent,\n    isTshirtSize,\n} from './validators'\n\nexport function getDefaultConfig() {\n    const colors = fromTheme('colors')\n    const spacing = fromTheme('spacing')\n    const blur = fromTheme('blur')\n    const brightness = fromTheme('brightness')\n    const borderColor = fromTheme('borderColor')\n    const borderRadius = fromTheme('borderRadius')\n    const borderSpacing = fromTheme('borderSpacing')\n    const borderWidth = fromTheme('borderWidth')\n    const contrast = fromTheme('contrast')\n    const grayscale = fromTheme('grayscale')\n    const hueRotate = fromTheme('hueRotate')\n    const invert = fromTheme('invert')\n    const gap = fromTheme('gap')\n    const gradientColorStops = fromTheme('gradientColorStops')\n    const gradientColorStopPositions = fromTheme('gradientColorStopPositions')\n    const inset = fromTheme('inset')\n    const margin = fromTheme('margin')\n    const opacity = fromTheme('opacity')\n    const padding = fromTheme('padding')\n    const saturate = fromTheme('saturate')\n    const scale = fromTheme('scale')\n    const sepia = fromTheme('sepia')\n    const skew = fromTheme('skew')\n    const space = fromTheme('space')\n    const translate = fromTheme('translate')\n\n    const getOverscroll = () => ['auto', 'contain', 'none'] as const\n    const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'] as const\n    const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing] as const\n    const getSpacingWithArbitrary = () => [isArbitraryValue, spacing] as const\n    const getLengthWithEmpty = () => ['', isLength] as const\n    const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue] as const\n    const getPositions = () =>\n        [\n            'bottom',\n            'center',\n            'left',\n            'left-bottom',\n            'left-top',\n            'right',\n            'right-bottom',\n            'right-top',\n            'top',\n        ] as const\n    const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'] as const\n    const getBlendModes = () =>\n        [\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity',\n            'plus-lighter',\n        ] as const\n    const getAlign = () =>\n        ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'] as const\n    const getZeroAndEmpty = () => ['', '0', isArbitraryValue] as const\n    const getBreaks = () =>\n        ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'] as const\n    const getNumber = () => [isNumber, isArbitraryNumber]\n    const getNumberAndArbitrary = () => [isNumber, isArbitraryValue]\n\n    return {\n        cacheSize: 500,\n        theme: {\n            colors: [isAny],\n            spacing: [isLength],\n            blur: ['none', '', isTshirtSize, isArbitraryValue],\n            brightness: getNumber(),\n            borderColor: [colors],\n            borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n            borderSpacing: getSpacingWithArbitrary(),\n            borderWidth: getLengthWithEmpty(),\n            contrast: getNumber(),\n            grayscale: getZeroAndEmpty(),\n            hueRotate: getNumberAndArbitrary(),\n            invert: getZeroAndEmpty(),\n            gap: getSpacingWithArbitrary(),\n            gradientColorStops: [colors],\n            gradientColorStopPositions: [isPercent, isArbitraryLength],\n            inset: getSpacingWithAutoAndArbitrary(),\n            margin: getSpacingWithAutoAndArbitrary(),\n            opacity: getNumber(),\n            padding: getSpacingWithArbitrary(),\n            saturate: getNumber(),\n            scale: getNumber(),\n            sepia: getZeroAndEmpty(),\n            skew: getNumberAndArbitrary(),\n            space: getSpacingWithArbitrary(),\n            translate: getSpacingWithArbitrary(),\n        },\n        classGroups: {\n            // Layout\n            /**\n             * Aspect Ratio\n             * @see https://tailwindcss.com/docs/aspect-ratio\n             */\n            aspect: [{ aspect: ['auto', 'square', 'video', isArbitraryValue] }],\n            /**\n             * Container\n             * @see https://tailwindcss.com/docs/container\n             */\n            container: ['container'],\n            /**\n             * Columns\n             * @see https://tailwindcss.com/docs/columns\n             */\n            columns: [{ columns: [isTshirtSize] }],\n            /**\n             * Break After\n             * @see https://tailwindcss.com/docs/break-after\n             */\n            'break-after': [{ 'break-after': getBreaks() }],\n            /**\n             * Break Before\n             * @see https://tailwindcss.com/docs/break-before\n             */\n            'break-before': [{ 'break-before': getBreaks() }],\n            /**\n             * Break Inside\n             * @see https://tailwindcss.com/docs/break-inside\n             */\n            'break-inside': [{ 'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column'] }],\n            /**\n             * Box Decoration Break\n             * @see https://tailwindcss.com/docs/box-decoration-break\n             */\n            'box-decoration': [{ 'box-decoration': ['slice', 'clone'] }],\n            /**\n             * Box Sizing\n             * @see https://tailwindcss.com/docs/box-sizing\n             */\n            box: [{ box: ['border', 'content'] }],\n            /**\n             * Display\n             * @see https://tailwindcss.com/docs/display\n             */\n            display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden',\n            ],\n            /**\n             * Floats\n             * @see https://tailwindcss.com/docs/float\n             */\n            float: [{ float: ['right', 'left', 'none'] }],\n            /**\n             * Clear\n             * @see https://tailwindcss.com/docs/clear\n             */\n            clear: [{ clear: ['left', 'right', 'both', 'none'] }],\n            /**\n             * Isolation\n             * @see https://tailwindcss.com/docs/isolation\n             */\n            isolation: ['isolate', 'isolation-auto'],\n            /**\n             * Object Fit\n             * @see https://tailwindcss.com/docs/object-fit\n             */\n            'object-fit': [{ object: ['contain', 'cover', 'fill', 'none', 'scale-down'] }],\n            /**\n             * Object Position\n             * @see https://tailwindcss.com/docs/object-position\n             */\n            'object-position': [{ object: [...getPositions(), isArbitraryValue] }],\n            /**\n             * Overflow\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            overflow: [{ overflow: getOverflow() }],\n            /**\n             * Overflow X\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-x': [{ 'overflow-x': getOverflow() }],\n            /**\n             * Overflow Y\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-y': [{ 'overflow-y': getOverflow() }],\n            /**\n             * Overscroll Behavior\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            overscroll: [{ overscroll: getOverscroll() }],\n            /**\n             * Overscroll Behavior X\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-x': [{ 'overscroll-x': getOverscroll() }],\n            /**\n             * Overscroll Behavior Y\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-y': [{ 'overscroll-y': getOverscroll() }],\n            /**\n             * Position\n             * @see https://tailwindcss.com/docs/position\n             */\n            position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n            /**\n             * Top / Right / Bottom / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            inset: [{ inset: [inset] }],\n            /**\n             * Right / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-x': [{ 'inset-x': [inset] }],\n            /**\n             * Top / Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-y': [{ 'inset-y': [inset] }],\n            /**\n             * Start\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            start: [{ start: [inset] }],\n            /**\n             * End\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            end: [{ end: [inset] }],\n            /**\n             * Top\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            top: [{ top: [inset] }],\n            /**\n             * Right\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            right: [{ right: [inset] }],\n            /**\n             * Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            bottom: [{ bottom: [inset] }],\n            /**\n             * Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            left: [{ left: [inset] }],\n            /**\n             * Visibility\n             * @see https://tailwindcss.com/docs/visibility\n             */\n            visibility: ['visible', 'invisible', 'collapse'],\n            /**\n             * Z-Index\n             * @see https://tailwindcss.com/docs/z-index\n             */\n            z: [{ z: ['auto', isInteger] }],\n            // Flexbox and Grid\n            /**\n             * Flex Basis\n             * @see https://tailwindcss.com/docs/flex-basis\n             */\n            basis: [{ basis: getSpacingWithAutoAndArbitrary() }],\n            /**\n             * Flex Direction\n             * @see https://tailwindcss.com/docs/flex-direction\n             */\n            'flex-direction': [{ flex: ['row', 'row-reverse', 'col', 'col-reverse'] }],\n            /**\n             * Flex Wrap\n             * @see https://tailwindcss.com/docs/flex-wrap\n             */\n            'flex-wrap': [{ flex: ['wrap', 'wrap-reverse', 'nowrap'] }],\n            /**\n             * Flex\n             * @see https://tailwindcss.com/docs/flex\n             */\n            flex: [{ flex: ['1', 'auto', 'initial', 'none', isArbitraryValue] }],\n            /**\n             * Flex Grow\n             * @see https://tailwindcss.com/docs/flex-grow\n             */\n            grow: [{ grow: getZeroAndEmpty() }],\n            /**\n             * Flex Shrink\n             * @see https://tailwindcss.com/docs/flex-shrink\n             */\n            shrink: [{ shrink: getZeroAndEmpty() }],\n            /**\n             * Order\n             * @see https://tailwindcss.com/docs/order\n             */\n            order: [{ order: ['first', 'last', 'none', isInteger] }],\n            /**\n             * Grid Template Columns\n             * @see https://tailwindcss.com/docs/grid-template-columns\n             */\n            'grid-cols': [{ 'grid-cols': [isAny] }],\n            /**\n             * Grid Column Start / End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start-end': [{ col: ['auto', { span: ['full', isInteger] }, isArbitraryValue] }],\n            /**\n             * Grid Column Start\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start': [{ 'col-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Column End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-end': [{ 'col-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Template Rows\n             * @see https://tailwindcss.com/docs/grid-template-rows\n             */\n            'grid-rows': [{ 'grid-rows': [isAny] }],\n            /**\n             * Grid Row Start / End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start-end': [{ row: ['auto', { span: [isInteger] }, isArbitraryValue] }],\n            /**\n             * Grid Row Start\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start': [{ 'row-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Row End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-end': [{ 'row-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Auto Flow\n             * @see https://tailwindcss.com/docs/grid-auto-flow\n             */\n            'grid-flow': [{ 'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense'] }],\n            /**\n             * Grid Auto Columns\n             * @see https://tailwindcss.com/docs/grid-auto-columns\n             */\n            'auto-cols': [{ 'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Grid Auto Rows\n             * @see https://tailwindcss.com/docs/grid-auto-rows\n             */\n            'auto-rows': [{ 'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Gap\n             * @see https://tailwindcss.com/docs/gap\n             */\n            gap: [{ gap: [gap] }],\n            /**\n             * Gap X\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-x': [{ 'gap-x': [gap] }],\n            /**\n             * Gap Y\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-y': [{ 'gap-y': [gap] }],\n            /**\n             * Justify Content\n             * @see https://tailwindcss.com/docs/justify-content\n             */\n            'justify-content': [{ justify: ['normal', ...getAlign()] }],\n            /**\n             * Justify Items\n             * @see https://tailwindcss.com/docs/justify-items\n             */\n            'justify-items': [{ 'justify-items': ['start', 'end', 'center', 'stretch'] }],\n            /**\n             * Justify Self\n             * @see https://tailwindcss.com/docs/justify-self\n             */\n            'justify-self': [{ 'justify-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            /**\n             * Align Content\n             * @see https://tailwindcss.com/docs/align-content\n             */\n            'align-content': [{ content: ['normal', ...getAlign(), 'baseline'] }],\n            /**\n             * Align Items\n             * @see https://tailwindcss.com/docs/align-items\n             */\n            'align-items': [{ items: ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Align Self\n             * @see https://tailwindcss.com/docs/align-self\n             */\n            'align-self': [{ self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline'] }],\n            /**\n             * Place Content\n             * @see https://tailwindcss.com/docs/place-content\n             */\n            'place-content': [{ 'place-content': [...getAlign(), 'baseline'] }],\n            /**\n             * Place Items\n             * @see https://tailwindcss.com/docs/place-items\n             */\n            'place-items': [{ 'place-items': ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Place Self\n             * @see https://tailwindcss.com/docs/place-self\n             */\n            'place-self': [{ 'place-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            // Spacing\n            /**\n             * Padding\n             * @see https://tailwindcss.com/docs/padding\n             */\n            p: [{ p: [padding] }],\n            /**\n             * Padding X\n             * @see https://tailwindcss.com/docs/padding\n             */\n            px: [{ px: [padding] }],\n            /**\n             * Padding Y\n             * @see https://tailwindcss.com/docs/padding\n             */\n            py: [{ py: [padding] }],\n            /**\n             * Padding Start\n             * @see https://tailwindcss.com/docs/padding\n             */\n            ps: [{ ps: [padding] }],\n            /**\n             * Padding End\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pe: [{ pe: [padding] }],\n            /**\n             * Padding Top\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pt: [{ pt: [padding] }],\n            /**\n             * Padding Right\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pr: [{ pr: [padding] }],\n            /**\n             * Padding Bottom\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pb: [{ pb: [padding] }],\n            /**\n             * Padding Left\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pl: [{ pl: [padding] }],\n            /**\n             * Margin\n             * @see https://tailwindcss.com/docs/margin\n             */\n            m: [{ m: [margin] }],\n            /**\n             * Margin X\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mx: [{ mx: [margin] }],\n            /**\n             * Margin Y\n             * @see https://tailwindcss.com/docs/margin\n             */\n            my: [{ my: [margin] }],\n            /**\n             * Margin Start\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ms: [{ ms: [margin] }],\n            /**\n             * Margin End\n             * @see https://tailwindcss.com/docs/margin\n             */\n            me: [{ me: [margin] }],\n            /**\n             * Margin Top\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mt: [{ mt: [margin] }],\n            /**\n             * Margin Right\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mr: [{ mr: [margin] }],\n            /**\n             * Margin Bottom\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mb: [{ mb: [margin] }],\n            /**\n             * Margin Left\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ml: [{ ml: [margin] }],\n            /**\n             * Space Between X\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x': [{ 'space-x': [space] }],\n            /**\n             * Space Between X Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x-reverse': ['space-x-reverse'],\n            /**\n             * Space Between Y\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y': [{ 'space-y': [space] }],\n            /**\n             * Space Between Y Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y-reverse': ['space-y-reverse'],\n            // Sizing\n            /**\n             * Width\n             * @see https://tailwindcss.com/docs/width\n             */\n            w: [{ w: ['auto', 'min', 'max', 'fit', isArbitraryValue, spacing] }],\n            /**\n             * Min-Width\n             * @see https://tailwindcss.com/docs/min-width\n             */\n            'min-w': [{ 'min-w': ['min', 'max', 'fit', isArbitraryValue, isLength] }],\n            /**\n             * Max-Width\n             * @see https://tailwindcss.com/docs/max-width\n             */\n            'max-w': [\n                {\n                    'max-w': [\n                        '0',\n                        'none',\n                        'full',\n                        'min',\n                        'max',\n                        'fit',\n                        'prose',\n                        { screen: [isTshirtSize] },\n                        isTshirtSize,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Height\n             * @see https://tailwindcss.com/docs/height\n             */\n            h: [{ h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit'] }],\n            /**\n             * Min-Height\n             * @see https://tailwindcss.com/docs/min-height\n             */\n            'min-h': [{ 'min-h': ['min', 'max', 'fit', isArbitraryValue, isLength] }],\n            /**\n             * Max-Height\n             * @see https://tailwindcss.com/docs/max-height\n             */\n            'max-h': [{ 'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit'] }],\n            // Typography\n            /**\n             * Font Size\n             * @see https://tailwindcss.com/docs/font-size\n             */\n            'font-size': [{ text: ['base', isTshirtSize, isArbitraryLength] }],\n            /**\n             * Font Smoothing\n             * @see https://tailwindcss.com/docs/font-smoothing\n             */\n            'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n            /**\n             * Font Style\n             * @see https://tailwindcss.com/docs/font-style\n             */\n            'font-style': ['italic', 'not-italic'],\n            /**\n             * Font Weight\n             * @see https://tailwindcss.com/docs/font-weight\n             */\n            'font-weight': [\n                {\n                    font: [\n                        'thin',\n                        'extralight',\n                        'light',\n                        'normal',\n                        'medium',\n                        'semibold',\n                        'bold',\n                        'extrabold',\n                        'black',\n                        isArbitraryNumber,\n                    ],\n                },\n            ],\n            /**\n             * Font Family\n             * @see https://tailwindcss.com/docs/font-family\n             */\n            'font-family': [{ font: [isAny] }],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-normal': ['normal-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-ordinal': ['ordinal'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-slashed-zero': ['slashed-zero'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-fraction': ['diagonal-fractions', 'stacked-fractons'],\n            /**\n             * Letter Spacing\n             * @see https://tailwindcss.com/docs/letter-spacing\n             */\n            tracking: [\n                {\n                    tracking: [\n                        'tighter',\n                        'tight',\n                        'normal',\n                        'wide',\n                        'wider',\n                        'widest',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Line Clamp\n             * @see https://tailwindcss.com/docs/line-clamp\n             */\n            'line-clamp': [{ 'line-clamp': ['none', isNumber, isArbitraryNumber] }],\n            /**\n             * Line Height\n             * @see https://tailwindcss.com/docs/line-height\n             */\n            leading: [\n                {\n                    leading: [\n                        'none',\n                        'tight',\n                        'snug',\n                        'normal',\n                        'relaxed',\n                        'loose',\n                        isArbitraryValue,\n                        isLength,\n                    ],\n                },\n            ],\n            /**\n             * List Style Image\n             * @see https://tailwindcss.com/docs/list-style-image\n             */\n            'list-image': [{ 'list-image': ['none', isArbitraryValue] }],\n            /**\n             * List Style Type\n             * @see https://tailwindcss.com/docs/list-style-type\n             */\n            'list-style-type': [{ list: ['none', 'disc', 'decimal', isArbitraryValue] }],\n            /**\n             * List Style Position\n             * @see https://tailwindcss.com/docs/list-style-position\n             */\n            'list-style-position': [{ list: ['inside', 'outside'] }],\n            /**\n             * Placeholder Color\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/placeholder-color\n             */\n            'placeholder-color': [{ placeholder: [colors] }],\n            /**\n             * Placeholder Opacity\n             * @see https://tailwindcss.com/docs/placeholder-opacity\n             */\n            'placeholder-opacity': [{ 'placeholder-opacity': [opacity] }],\n            /**\n             * Text Alignment\n             * @see https://tailwindcss.com/docs/text-align\n             */\n            'text-alignment': [{ text: ['left', 'center', 'right', 'justify', 'start', 'end'] }],\n            /**\n             * Text Color\n             * @see https://tailwindcss.com/docs/text-color\n             */\n            'text-color': [{ text: [colors] }],\n            /**\n             * Text Opacity\n             * @see https://tailwindcss.com/docs/text-opacity\n             */\n            'text-opacity': [{ 'text-opacity': [opacity] }],\n            /**\n             * Text Decoration\n             * @see https://tailwindcss.com/docs/text-decoration\n             */\n            'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n            /**\n             * Text Decoration Style\n             * @see https://tailwindcss.com/docs/text-decoration-style\n             */\n            'text-decoration-style': [{ decoration: [...getLineStyles(), 'wavy'] }],\n            /**\n             * Text Decoration Thickness\n             * @see https://tailwindcss.com/docs/text-decoration-thickness\n             */\n            'text-decoration-thickness': [{ decoration: ['auto', 'from-font', isLength] }],\n            /**\n             * Text Underline Offset\n             * @see https://tailwindcss.com/docs/text-underline-offset\n             */\n            'underline-offset': [{ 'underline-offset': ['auto', isArbitraryValue, isLength] }],\n            /**\n             * Text Decoration Color\n             * @see https://tailwindcss.com/docs/text-decoration-color\n             */\n            'text-decoration-color': [{ decoration: [colors] }],\n            /**\n             * Text Transform\n             * @see https://tailwindcss.com/docs/text-transform\n             */\n            'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n            /**\n             * Text Overflow\n             * @see https://tailwindcss.com/docs/text-overflow\n             */\n            'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n            /**\n             * Text Indent\n             * @see https://tailwindcss.com/docs/text-indent\n             */\n            indent: [{ indent: getSpacingWithArbitrary() }],\n            /**\n             * Vertical Alignment\n             * @see https://tailwindcss.com/docs/vertical-align\n             */\n            'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Whitespace\n             * @see https://tailwindcss.com/docs/whitespace\n             */\n            whitespace: [\n                { whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces'] },\n            ],\n            /**\n             * Word Break\n             * @see https://tailwindcss.com/docs/word-break\n             */\n            break: [{ break: ['normal', 'words', 'all', 'keep'] }],\n            /**\n             * Hyphens\n             * @see https://tailwindcss.com/docs/hyphens\n             */\n            hyphens: [{ hyphens: ['none', 'manual', 'auto'] }],\n            /**\n             * Content\n             * @see https://tailwindcss.com/docs/content\n             */\n            content: [{ content: ['none', isArbitraryValue] }],\n            // Backgrounds\n            /**\n             * Background Attachment\n             * @see https://tailwindcss.com/docs/background-attachment\n             */\n            'bg-attachment': [{ bg: ['fixed', 'local', 'scroll'] }],\n            /**\n             * Background Clip\n             * @see https://tailwindcss.com/docs/background-clip\n             */\n            'bg-clip': [{ 'bg-clip': ['border', 'padding', 'content', 'text'] }],\n            /**\n             * Background Opacity\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/background-opacity\n             */\n            'bg-opacity': [{ 'bg-opacity': [opacity] }],\n            /**\n             * Background Origin\n             * @see https://tailwindcss.com/docs/background-origin\n             */\n            'bg-origin': [{ 'bg-origin': ['border', 'padding', 'content'] }],\n            /**\n             * Background Position\n             * @see https://tailwindcss.com/docs/background-position\n             */\n            'bg-position': [{ bg: [...getPositions(), isArbitraryPosition] }],\n            /**\n             * Background Repeat\n             * @see https://tailwindcss.com/docs/background-repeat\n             */\n            'bg-repeat': [{ bg: ['no-repeat', { repeat: ['', 'x', 'y', 'round', 'space'] }] }],\n            /**\n             * Background Size\n             * @see https://tailwindcss.com/docs/background-size\n             */\n            'bg-size': [{ bg: ['auto', 'cover', 'contain', isArbitrarySize] }],\n            /**\n             * Background Image\n             * @see https://tailwindcss.com/docs/background-image\n             */\n            'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        { 'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl'] },\n                        isArbitraryUrl,\n                    ],\n                },\n            ],\n            /**\n             * Background Color\n             * @see https://tailwindcss.com/docs/background-color\n             */\n            'bg-color': [{ bg: [colors] }],\n            /**\n             * Gradient Color Stops From Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from-pos': [{ from: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops Via Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via-pos': [{ via: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops To Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to-pos': [{ to: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops From\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from': [{ from: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops Via\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via': [{ via: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops To\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to': [{ to: [gradientColorStops] }],\n            // Borders\n            /**\n             * Border Radius\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            rounded: [{ rounded: [borderRadius] }],\n            /**\n             * Border Radius Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-s': [{ 'rounded-s': [borderRadius] }],\n            /**\n             * Border Radius End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-e': [{ 'rounded-e': [borderRadius] }],\n            /**\n             * Border Radius Top\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-t': [{ 'rounded-t': [borderRadius] }],\n            /**\n             * Border Radius Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-r': [{ 'rounded-r': [borderRadius] }],\n            /**\n             * Border Radius Bottom\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-b': [{ 'rounded-b': [borderRadius] }],\n            /**\n             * Border Radius Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-l': [{ 'rounded-l': [borderRadius] }],\n            /**\n             * Border Radius Start Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ss': [{ 'rounded-ss': [borderRadius] }],\n            /**\n             * Border Radius Start End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-se': [{ 'rounded-se': [borderRadius] }],\n            /**\n             * Border Radius End End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ee': [{ 'rounded-ee': [borderRadius] }],\n            /**\n             * Border Radius End Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-es': [{ 'rounded-es': [borderRadius] }],\n            /**\n             * Border Radius Top Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tl': [{ 'rounded-tl': [borderRadius] }],\n            /**\n             * Border Radius Top Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tr': [{ 'rounded-tr': [borderRadius] }],\n            /**\n             * Border Radius Bottom Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-br': [{ 'rounded-br': [borderRadius] }],\n            /**\n             * Border Radius Bottom Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-bl': [{ 'rounded-bl': [borderRadius] }],\n            /**\n             * Border Width\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w': [{ border: [borderWidth] }],\n            /**\n             * Border Width X\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-x': [{ 'border-x': [borderWidth] }],\n            /**\n             * Border Width Y\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-y': [{ 'border-y': [borderWidth] }],\n            /**\n             * Border Width Start\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-s': [{ 'border-s': [borderWidth] }],\n            /**\n             * Border Width End\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-e': [{ 'border-e': [borderWidth] }],\n            /**\n             * Border Width Top\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-t': [{ 'border-t': [borderWidth] }],\n            /**\n             * Border Width Right\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-r': [{ 'border-r': [borderWidth] }],\n            /**\n             * Border Width Bottom\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-b': [{ 'border-b': [borderWidth] }],\n            /**\n             * Border Width Left\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-l': [{ 'border-l': [borderWidth] }],\n            /**\n             * Border Opacity\n             * @see https://tailwindcss.com/docs/border-opacity\n             */\n            'border-opacity': [{ 'border-opacity': [opacity] }],\n            /**\n             * Border Style\n             * @see https://tailwindcss.com/docs/border-style\n             */\n            'border-style': [{ border: [...getLineStyles(), 'hidden'] }],\n            /**\n             * Divide Width X\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x': [{ 'divide-x': [borderWidth] }],\n            /**\n             * Divide Width X Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x-reverse': ['divide-x-reverse'],\n            /**\n             * Divide Width Y\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y': [{ 'divide-y': [borderWidth] }],\n            /**\n             * Divide Width Y Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y-reverse': ['divide-y-reverse'],\n            /**\n             * Divide Opacity\n             * @see https://tailwindcss.com/docs/divide-opacity\n             */\n            'divide-opacity': [{ 'divide-opacity': [opacity] }],\n            /**\n             * Divide Style\n             * @see https://tailwindcss.com/docs/divide-style\n             */\n            'divide-style': [{ divide: getLineStyles() }],\n            /**\n             * Border Color\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color': [{ border: [borderColor] }],\n            /**\n             * Border Color X\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-x': [{ 'border-x': [borderColor] }],\n            /**\n             * Border Color Y\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-y': [{ 'border-y': [borderColor] }],\n            /**\n             * Border Color Top\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-t': [{ 'border-t': [borderColor] }],\n            /**\n             * Border Color Right\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-r': [{ 'border-r': [borderColor] }],\n            /**\n             * Border Color Bottom\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-b': [{ 'border-b': [borderColor] }],\n            /**\n             * Border Color Left\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-l': [{ 'border-l': [borderColor] }],\n            /**\n             * Divide Color\n             * @see https://tailwindcss.com/docs/divide-color\n             */\n            'divide-color': [{ divide: [borderColor] }],\n            /**\n             * Outline Style\n             * @see https://tailwindcss.com/docs/outline-style\n             */\n            'outline-style': [{ outline: ['', ...getLineStyles()] }],\n            /**\n             * Outline Offset\n             * @see https://tailwindcss.com/docs/outline-offset\n             */\n            'outline-offset': [{ 'outline-offset': [isArbitraryValue, isLength] }],\n            /**\n             * Outline Width\n             * @see https://tailwindcss.com/docs/outline-width\n             */\n            'outline-w': [{ outline: [isLength] }],\n            /**\n             * Outline Color\n             * @see https://tailwindcss.com/docs/outline-color\n             */\n            'outline-color': [{ outline: [colors] }],\n            /**\n             * Ring Width\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w': [{ ring: getLengthWithEmpty() }],\n            /**\n             * Ring Width Inset\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w-inset': ['ring-inset'],\n            /**\n             * Ring Color\n             * @see https://tailwindcss.com/docs/ring-color\n             */\n            'ring-color': [{ ring: [colors] }],\n            /**\n             * Ring Opacity\n             * @see https://tailwindcss.com/docs/ring-opacity\n             */\n            'ring-opacity': [{ 'ring-opacity': [opacity] }],\n            /**\n             * Ring Offset Width\n             * @see https://tailwindcss.com/docs/ring-offset-width\n             */\n            'ring-offset-w': [{ 'ring-offset': [isLength] }],\n            /**\n             * Ring Offset Color\n             * @see https://tailwindcss.com/docs/ring-offset-color\n             */\n            'ring-offset-color': [{ 'ring-offset': [colors] }],\n            // Effects\n            /**\n             * Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow\n             */\n            shadow: [{ shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow] }],\n            /**\n             * Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow-color\n             */\n            'shadow-color': [{ shadow: [isAny] }],\n            /**\n             * Opacity\n             * @see https://tailwindcss.com/docs/opacity\n             */\n            opacity: [{ opacity: [opacity] }],\n            /**\n             * Mix Blend Mode\n             * @see https://tailwindcss.com/docs/mix-blend-mode\n             */\n            'mix-blend': [{ 'mix-blend': getBlendModes() }],\n            /**\n             * Background Blend Mode\n             * @see https://tailwindcss.com/docs/background-blend-mode\n             */\n            'bg-blend': [{ 'bg-blend': getBlendModes() }],\n            // Filters\n            /**\n             * Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/filter\n             */\n            filter: [{ filter: ['', 'none'] }],\n            /**\n             * Blur\n             * @see https://tailwindcss.com/docs/blur\n             */\n            blur: [{ blur: [blur] }],\n            /**\n             * Brightness\n             * @see https://tailwindcss.com/docs/brightness\n             */\n            brightness: [{ brightness: [brightness] }],\n            /**\n             * Contrast\n             * @see https://tailwindcss.com/docs/contrast\n             */\n            contrast: [{ contrast: [contrast] }],\n            /**\n             * Drop Shadow\n             * @see https://tailwindcss.com/docs/drop-shadow\n             */\n            'drop-shadow': [{ 'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue] }],\n            /**\n             * Grayscale\n             * @see https://tailwindcss.com/docs/grayscale\n             */\n            grayscale: [{ grayscale: [grayscale] }],\n            /**\n             * Hue Rotate\n             * @see https://tailwindcss.com/docs/hue-rotate\n             */\n            'hue-rotate': [{ 'hue-rotate': [hueRotate] }],\n            /**\n             * Invert\n             * @see https://tailwindcss.com/docs/invert\n             */\n            invert: [{ invert: [invert] }],\n            /**\n             * Saturate\n             * @see https://tailwindcss.com/docs/saturate\n             */\n            saturate: [{ saturate: [saturate] }],\n            /**\n             * Sepia\n             * @see https://tailwindcss.com/docs/sepia\n             */\n            sepia: [{ sepia: [sepia] }],\n            /**\n             * Backdrop Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/backdrop-filter\n             */\n            'backdrop-filter': [{ 'backdrop-filter': ['', 'none'] }],\n            /**\n             * Backdrop Blur\n             * @see https://tailwindcss.com/docs/backdrop-blur\n             */\n            'backdrop-blur': [{ 'backdrop-blur': [blur] }],\n            /**\n             * Backdrop Brightness\n             * @see https://tailwindcss.com/docs/backdrop-brightness\n             */\n            'backdrop-brightness': [{ 'backdrop-brightness': [brightness] }],\n            /**\n             * Backdrop Contrast\n             * @see https://tailwindcss.com/docs/backdrop-contrast\n             */\n            'backdrop-contrast': [{ 'backdrop-contrast': [contrast] }],\n            /**\n             * Backdrop Grayscale\n             * @see https://tailwindcss.com/docs/backdrop-grayscale\n             */\n            'backdrop-grayscale': [{ 'backdrop-grayscale': [grayscale] }],\n            /**\n             * Backdrop Hue Rotate\n             * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n             */\n            'backdrop-hue-rotate': [{ 'backdrop-hue-rotate': [hueRotate] }],\n            /**\n             * Backdrop Invert\n             * @see https://tailwindcss.com/docs/backdrop-invert\n             */\n            'backdrop-invert': [{ 'backdrop-invert': [invert] }],\n            /**\n             * Backdrop Opacity\n             * @see https://tailwindcss.com/docs/backdrop-opacity\n             */\n            'backdrop-opacity': [{ 'backdrop-opacity': [opacity] }],\n            /**\n             * Backdrop Saturate\n             * @see https://tailwindcss.com/docs/backdrop-saturate\n             */\n            'backdrop-saturate': [{ 'backdrop-saturate': [saturate] }],\n            /**\n             * Backdrop Sepia\n             * @see https://tailwindcss.com/docs/backdrop-sepia\n             */\n            'backdrop-sepia': [{ 'backdrop-sepia': [sepia] }],\n            // Tables\n            /**\n             * Border Collapse\n             * @see https://tailwindcss.com/docs/border-collapse\n             */\n            'border-collapse': [{ border: ['collapse', 'separate'] }],\n            /**\n             * Border Spacing\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing': [{ 'border-spacing': [borderSpacing] }],\n            /**\n             * Border Spacing X\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-x': [{ 'border-spacing-x': [borderSpacing] }],\n            /**\n             * Border Spacing Y\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-y': [{ 'border-spacing-y': [borderSpacing] }],\n            /**\n             * Table Layout\n             * @see https://tailwindcss.com/docs/table-layout\n             */\n            'table-layout': [{ table: ['auto', 'fixed'] }],\n            /**\n             * Caption Side\n             * @see https://tailwindcss.com/docs/caption-side\n             */\n            caption: [{ caption: ['top', 'bottom'] }],\n            // Transitions and Animation\n            /**\n             * Tranisition Property\n             * @see https://tailwindcss.com/docs/transition-property\n             */\n            transition: [\n                {\n                    transition: [\n                        'none',\n                        'all',\n                        '',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Transition Duration\n             * @see https://tailwindcss.com/docs/transition-duration\n             */\n            duration: [{ duration: getNumberAndArbitrary() }],\n            /**\n             * Transition Timing Function\n             * @see https://tailwindcss.com/docs/transition-timing-function\n             */\n            ease: [{ ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue] }],\n            /**\n             * Transition Delay\n             * @see https://tailwindcss.com/docs/transition-delay\n             */\n            delay: [{ delay: getNumberAndArbitrary() }],\n            /**\n             * Animation\n             * @see https://tailwindcss.com/docs/animation\n             */\n            animate: [{ animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue] }],\n            // Transforms\n            /**\n             * Transform\n             * @see https://tailwindcss.com/docs/transform\n             */\n            transform: [{ transform: ['', 'gpu', 'none'] }],\n            /**\n             * Scale\n             * @see https://tailwindcss.com/docs/scale\n             */\n            scale: [{ scale: [scale] }],\n            /**\n             * Scale X\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-x': [{ 'scale-x': [scale] }],\n            /**\n             * Scale Y\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-y': [{ 'scale-y': [scale] }],\n            /**\n             * Rotate\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            rotate: [{ rotate: [isInteger, isArbitraryValue] }],\n            /**\n             * Translate X\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-x': [{ 'translate-x': [translate] }],\n            /**\n             * Translate Y\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-y': [{ 'translate-y': [translate] }],\n            /**\n             * Skew X\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-x': [{ 'skew-x': [skew] }],\n            /**\n             * Skew Y\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-y': [{ 'skew-y': [skew] }],\n            /**\n             * Transform Origin\n             * @see https://tailwindcss.com/docs/transform-origin\n             */\n            'transform-origin': [\n                {\n                    origin: [\n                        'center',\n                        'top',\n                        'top-right',\n                        'right',\n                        'bottom-right',\n                        'bottom',\n                        'bottom-left',\n                        'left',\n                        'top-left',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            // Interactivity\n            /**\n             * Accent Color\n             * @see https://tailwindcss.com/docs/accent-color\n             */\n            accent: [{ accent: ['auto', colors] }],\n            /**\n             * Appearance\n             * @see https://tailwindcss.com/docs/appearance\n             */\n            appearance: ['appearance-none'],\n            /**\n             * Cursor\n             * @see https://tailwindcss.com/docs/cursor\n             */\n            cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Caret Color\n             * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n             */\n            'caret-color': [{ caret: [colors] }],\n            /**\n             * Pointer Events\n             * @see https://tailwindcss.com/docs/pointer-events\n             */\n            'pointer-events': [{ 'pointer-events': ['none', 'auto'] }],\n            /**\n             * Resize\n             * @see https://tailwindcss.com/docs/resize\n             */\n            resize: [{ resize: ['none', 'y', 'x', ''] }],\n            /**\n             * Scroll Behavior\n             * @see https://tailwindcss.com/docs/scroll-behavior\n             */\n            'scroll-behavior': [{ scroll: ['auto', 'smooth'] }],\n            /**\n             * Scroll Margin\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-m': [{ 'scroll-m': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin X\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mx': [{ 'scroll-mx': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Y\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-my': [{ 'scroll-my': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Start\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ms': [{ 'scroll-ms': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin End\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-me': [{ 'scroll-me': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Top\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mt': [{ 'scroll-mt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Right\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mr': [{ 'scroll-mr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Bottom\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mb': [{ 'scroll-mb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Left\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ml': [{ 'scroll-ml': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-p': [{ 'scroll-p': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding X\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-px': [{ 'scroll-px': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Y\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-py': [{ 'scroll-py': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Start\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-ps': [{ 'scroll-ps': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding End\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pe': [{ 'scroll-pe': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Top\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pt': [{ 'scroll-pt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Right\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pr': [{ 'scroll-pr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Bottom\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pb': [{ 'scroll-pb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Left\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pl': [{ 'scroll-pl': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Snap Align\n             * @see https://tailwindcss.com/docs/scroll-snap-align\n             */\n            'snap-align': [{ snap: ['start', 'end', 'center', 'align-none'] }],\n            /**\n             * Scroll Snap Stop\n             * @see https://tailwindcss.com/docs/scroll-snap-stop\n             */\n            'snap-stop': [{ snap: ['normal', 'always'] }],\n            /**\n             * Scroll Snap Type\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-type': [{ snap: ['none', 'x', 'y', 'both'] }],\n            /**\n             * Scroll Snap Type Strictness\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-strictness': [{ snap: ['mandatory', 'proximity'] }],\n            /**\n             * Touch Action\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            touch: [\n                {\n                    touch: [\n                        'auto',\n                        'none',\n                        'pinch-zoom',\n                        'manipulation',\n                        { pan: ['x', 'left', 'right', 'y', 'up', 'down'] },\n                    ],\n                },\n            ],\n            /**\n             * User Select\n             * @see https://tailwindcss.com/docs/user-select\n             */\n            select: [{ select: ['none', 'text', 'all', 'auto'] }],\n            /**\n             * Will Change\n             * @see https://tailwindcss.com/docs/will-change\n             */\n            'will-change': [\n                { 'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue] },\n            ],\n            // SVG\n            /**\n             * Fill\n             * @see https://tailwindcss.com/docs/fill\n             */\n            fill: [{ fill: [colors, 'none'] }],\n            /**\n             * Stroke Width\n             * @see https://tailwindcss.com/docs/stroke-width\n             */\n            'stroke-w': [{ stroke: [isLength, isArbitraryNumber] }],\n            /**\n             * Stroke\n             * @see https://tailwindcss.com/docs/stroke\n             */\n            stroke: [{ stroke: [colors, 'none'] }],\n            // Accessibility\n            /**\n             * Screen Readers\n             * @see https://tailwindcss.com/docs/screen-readers\n             */\n            sr: ['sr-only', 'not-sr-only'],\n        },\n        conflictingClassGroups: {\n            overflow: ['overflow-x', 'overflow-y'],\n            overscroll: ['overscroll-x', 'overscroll-y'],\n            inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n            'inset-x': ['right', 'left'],\n            'inset-y': ['top', 'bottom'],\n            flex: ['basis', 'grow', 'shrink'],\n            gap: ['gap-x', 'gap-y'],\n            p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n            px: ['pr', 'pl'],\n            py: ['pt', 'pb'],\n            m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n            mx: ['mr', 'ml'],\n            my: ['mt', 'mb'],\n            'font-size': ['leading'],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction',\n            ],\n            'fvn-ordinal': ['fvn-normal'],\n            'fvn-slashed-zero': ['fvn-normal'],\n            'fvn-figure': ['fvn-normal'],\n            'fvn-spacing': ['fvn-normal'],\n            'fvn-fraction': ['fvn-normal'],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl',\n            ],\n            'rounded-s': ['rounded-ss', 'rounded-es'],\n            'rounded-e': ['rounded-se', 'rounded-ee'],\n            'rounded-t': ['rounded-tl', 'rounded-tr'],\n            'rounded-r': ['rounded-tr', 'rounded-br'],\n            'rounded-b': ['rounded-br', 'rounded-bl'],\n            'rounded-l': ['rounded-tl', 'rounded-bl'],\n            'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n            'border-w': [\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l',\n            ],\n            'border-w-x': ['border-w-r', 'border-w-l'],\n            'border-w-y': ['border-w-t', 'border-w-b'],\n            'border-color': [\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l',\n            ],\n            'border-color-x': ['border-color-r', 'border-color-l'],\n            'border-color-y': ['border-color-t', 'border-color-b'],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml',\n            ],\n            'scroll-mx': ['scroll-mr', 'scroll-ml'],\n            'scroll-my': ['scroll-mt', 'scroll-mb'],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl',\n            ],\n            'scroll-px': ['scroll-pr', 'scroll-pl'],\n            'scroll-py': ['scroll-pt', 'scroll-pb'],\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': ['leading'],\n        },\n    } as const satisfies Config\n}\n", "import { Config } from './types'\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nexport function mergeConfigs(baseConfig: Config, configExtension: Partial<Config>) {\n    for (const key in configExtension) {\n        mergePropertyRecursively(baseConfig as any, key, configExtension[key as keyof Config])\n    }\n\n    return baseConfig\n}\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty\nconst overrideTypes = new Set(['string', 'number', 'boolean'])\n\nfunction mergePropertyRecursively(\n    baseObject: Record<string, unknown>,\n    mergeKey: string,\n    mergeValue: unknown,\n) {\n    if (\n        !hasOwnProperty.call(baseObject, mergeKey) ||\n        overrideTypes.has(typeof mergeValue) ||\n        mergeValue === null\n    ) {\n        baseObject[mergeKey] = mergeValue\n        return\n    }\n\n    if (Array.isArray(mergeValue) && Array.isArray(baseObject[mergeKey])) {\n        baseObject[mergeKey] = (baseObject[mergeKey] as unknown[]).concat(mergeValue)\n        return\n    }\n\n    if (typeof mergeValue === 'object' && typeof baseObject[mergeKey] === 'object') {\n        if (baseObject[mergeKey] === null) {\n            baseObject[mergeKey] = mergeValue\n            return\n        }\n\n        for (const nextKey in mergeValue) {\n            mergePropertyRecursively(\n                baseObject[mergeKey] as Record<string, unknown>,\n                nextKey,\n                mergeValue[nextKey as keyof object],\n            )\n        }\n    }\n}\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\nimport { mergeConfigs } from './merge-configs'\nimport { Config } from './types'\n\ntype CreateConfigSubsequent = (config: Config) => Config\n\nexport function extendTailwindMerge(\n    configExtension: Partial<Config> | CreateConfigSubsequent,\n    ...createConfig: CreateConfigSubsequent[]\n) {\n    return typeof configExtension === 'function'\n        ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig)\n        : createTailwindMerge(\n              () => mergeConfigs(getDefaultConfig(), configExtension),\n              ...createConfig,\n          )\n}\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\n\nexport const twMerge = createTailwindMerge(getDefaultConfig)\n", "import { twJoin } from './lib/tw-join'\n\nexport { createTailwindMerge } from './lib/create-tailwind-merge'\nexport { getDefaultConfig } from './lib/default-config'\nexport { extendTailwindMerge } from './lib/extend-tailwind-merge'\nexport { fromTheme } from './lib/from-theme'\nexport { mergeConfigs } from './lib/merge-configs'\nexport { twJoin, type ClassNameValue } from './lib/tw-join'\nexport { twMerge } from './lib/tw-merge'\nexport type { Config } from './lib/types'\nexport * as validators from './lib/validators'\n\n/**\n * @deprecated Will be removed in next major version. Use `twJoin` instead.\n */\nexport const join = twJoin\n"], "mappings": ";;;;;SAcgBA,SAAM;AAClB,MAAIC,QAAQ;AACZ,MAAIC;AACJ,MAAIC;AACJ,MAAIC,SAAS;AAEb,SAAOH,QAAQI,UAAUC,QAAQ;AAC7B,QAAKJ,WAAWG,UAAUJ,OAAO,GAAI;AACjC,UAAKE,gBAAgBI,QAAQL,QAAQ,GAAI;AACrCE,mBAAWA,UAAU;AACrBA,kBAAUD;MACb;IACJ;EACJ;AACD,SAAOC;AACX;AAEA,SAASG,QAAQC,KAA4B;AACzC,MAAI,OAAOA,QAAQ,UAAU;AACzB,WAAOA;EACV;AAED,MAAIL;AACJ,MAAIC,SAAS;AAEb,WAASK,IAAI,GAAGA,IAAID,IAAIF,QAAQG,KAAK;AACjC,QAAID,IAAIC,CAAC,GAAG;AACR,UAAKN,gBAAgBI,QAAQC,IAAIC,CAAC,CAA4B,GAAI;AAC9DL,mBAAWA,UAAU;AACrBA,kBAAUD;MACb;IACJ;EACJ;AAED,SAAOC;AACX;;;ACpCA,IAAMM,uBAAuB;AAEvB,SAAUC,iBAAiBC,QAAc;AAC3C,MAAMC,WAAWC,eAAeF,MAAM;AACtC,MAAQG,yBAAgEH,OAAhEG,wBAAsB,wBAA0CH,OAAxCI,gCAAAA,iCAA8B,0BAAA,SAAG,CAAA,IAAE;AAEnE,WAASC,gBAAgBC,WAAiB;AACtC,QAAMC,aAAaD,UAAUE,MAAMV,oBAAoB;AAGvD,QAAIS,WAAW,CAAC,MAAM,MAAMA,WAAWE,WAAW,GAAG;AACjDF,iBAAWG,MAAK;IACnB;AAED,WAAOC,kBAAkBJ,YAAYN,QAAQ,KAAKW,+BAA+BN,SAAS;EAC9F;AAEA,WAASO,4BAA4BC,cAA4BC,oBAA2B;AACxF,QAAMC,YAAYb,uBAAuBW,YAAY,KAAK,CAAA;AAE1D,QAAIC,sBAAsBX,+BAA+BU,YAAY,GAAG;AACpE,aAAA,CAAA,EAAA,OAAWE,WAAcZ,+BAA+BU,YAAY,CAAE;IACzE;AAED,WAAOE;EACX;AAEA,SAAO;IACHX;IACAQ;;AAER;AAEA,SAASF,kBACLJ,YACAU,iBAAgC;AAnCpC;AAqCI,MAAIV,WAAWE,WAAW,GAAG;AACzB,WAAOQ,gBAAgBH;EAC1B;AAED,MAAMI,mBAAmBX,WAAW,CAAC;AACrC,MAAMY,sBAAsBF,gBAAgBG,SAASC,IAAIH,gBAAgB;AACzE,MAAMI,8BAA8BH,sBAC9BR,kBAAkBJ,WAAWgB,MAAM,CAAC,GAAGJ,mBAAmB,IAC1DK;AAEN,MAAIF,6BAA6B;AAC7B,WAAOA;EACV;AAED,MAAIL,gBAAgBQ,WAAWhB,WAAW,GAAG;AACzC,WAAOe;EACV;AAED,MAAME,YAAYnB,WAAWoB,KAAK7B,oBAAoB;AAEtD,UAAOmB,qBAAgBQ,WAAWG,KAAK,SAAA,MAAA;AAAA,QAAGC,YAAS,KAATA;AAAS,WAAOA,UAAUH,SAAS;EAAC,CAAA,MAAvET,mBAA0EH;AACrF;AAEA,IAAMgB,yBAAyB;AAE/B,SAASlB,+BAA+BN,WAAiB;AACrD,MAAIwB,uBAAuBC,KAAKzB,SAAS,GAAG;AACxC,QAAM0B,6BAA6BF,uBAAuBG,KAAK3B,SAAS,EAAG,CAAC;AAC5E,QAAM4B,WAAWF,yEAA4BG,UACzC,GACAH,2BAA2BI,QAAQ,GAAG;AAG1C,QAAIF,UAAU;AAEV,aAAO,gBAAgBA;IAC1B;EACJ;AACL;AAKM,SAAUhC,eAAeF,QAAc;AACzC,MAAQqC,QAAkBrC,OAAlBqC,OAAOC,SAAWtC,OAAXsC;AACf,MAAMrC,WAA4B;IAC9BmB,UAAU,oBAAImB,IAAG;IACjBd,YAAY,CAAA;;AAGhB,MAAMe,4BAA4BC,6BAC9BC,OAAOC,QAAQ3C,OAAO4C,WAAW,GACjCN,MAAM;AAGVE,4BAA0BK,QAAQ,SAA+B,OAAA;AAAA,QAA7B/B,eAAY,MAAA,CAAA,GAAEgC,aAAU,MAAA,CAAA;AACxDC,8BAA0BD,YAAY7C,UAAUa,cAAcuB,KAAK;EACvE,CAAC;AAED,SAAOpC;AACX;AAEA,SAAS8C,0BACLD,YACA7B,iBACAH,cACAuB,OAAkB;AAElBS,aAAWD,QAAQ,SAACG,iBAAmB;AACnC,QAAI,OAAOA,oBAAoB,UAAU;AACrC,UAAMC,wBACFD,oBAAoB,KAAK/B,kBAAkBiC,QAAQjC,iBAAiB+B,eAAe;AACvFC,4BAAsBnC,eAAeA;AACrC;IACH;AAED,QAAI,OAAOkC,oBAAoB,YAAY;AACvC,UAAIG,cAAcH,eAAe,GAAG;AAChCD,kCACIC,gBAAgBX,KAAK,GACrBpB,iBACAH,cACAuB,KAAK;AAET;MACH;AAEDpB,sBAAgBQ,WAAW2B,KAAK;QAC5BvB,WAAWmB;QACXlC;MACH,CAAA;AAED;IACH;AAED4B,WAAOC,QAAQK,eAAe,EAAEH,QAAQ,SAAsB,OAAA;AAAA,UAApBQ,MAAG,MAAA,CAAA,GAAEP,cAAU,MAAA,CAAA;AACrDC,gCACID,aACAI,QAAQjC,iBAAiBoC,GAAG,GAC5BvC,cACAuB,KAAK;IAEb,CAAC;EACL,CAAC;AACL;AAEA,SAASa,QAAQjC,iBAAkCqC,MAAY;AAC3D,MAAIC,yBAAyBtC;AAE7BqC,OAAK9C,MAAMV,oBAAoB,EAAE+C,QAAQ,SAACW,UAAY;AAClD,QAAI,CAACD,uBAAuBnC,SAASqC,IAAID,QAAQ,GAAG;AAChDD,6BAAuBnC,SAASsC,IAAIF,UAAU;QAC1CpC,UAAU,oBAAImB,IAAG;QACjBd,YAAY,CAAA;MACf,CAAA;IACJ;AAED8B,6BAAyBA,uBAAuBnC,SAASC,IAAImC,QAAQ;EACzE,CAAC;AAED,SAAOD;AACX;AAEA,SAASJ,cAAcQ,MAAkC;AACrD,SAAQA,KAAqBR;AACjC;AAEA,SAASV,6BACLmB,mBACAtB,QAA0B;AAE1B,MAAI,CAACA,QAAQ;AACT,WAAOsB;EACV;AAED,SAAOA,kBAAkBC,IAAI,SAA+B,OAAA;AAAA,QAA7B/C,eAAY,MAAA,CAAA,GAAEgC,aAAU,MAAA,CAAA;AACnD,QAAMgB,qBAAqBhB,WAAWe,IAAI,SAACb,iBAAmB;AAC1D,UAAI,OAAOA,oBAAoB,UAAU;AACrC,eAAOV,SAASU;MACnB;AAED,UAAI,OAAOA,oBAAoB,UAAU;AACrC,eAAON,OAAOqB,YACVrB,OAAOC,QAAQK,eAAe,EAAEa,IAAI,SAAA,OAAA;AAAA,cAAER,MAAG,MAAA,CAAA,GAAEW,QAAK,MAAA,CAAA;AAAA,iBAAM,CAAC1B,SAASe,KAAKW,KAAK;QAAC,CAAA,CAAC;MAEnF;AAED,aAAOhB;IACX,CAAC;AAED,WAAO,CAAClC,cAAcgD,kBAAkB;EAC5C,CAAC;AACL;;;AClMM,SAAUG,eAA2BC,cAAoB;AAC3D,MAAIA,eAAe,GAAG;AAClB,WAAO;MACHC,KAAK,SAAA,MAAA;AAAA,eAAMC;MAAS;MACpBC,KAAK,SAAA,MAAK;MAAA;;EAEjB;AAED,MAAIC,YAAY;AAChB,MAAIC,QAAQ,oBAAIC,IAAG;AACnB,MAAIC,gBAAgB,oBAAID,IAAG;AAE3B,WAASE,OAAOC,KAAUC,OAAY;AAClCL,UAAMF,IAAIM,KAAKC,KAAK;AACpBN;AAEA,QAAIA,YAAYJ,cAAc;AAC1BI,kBAAY;AACZG,sBAAgBF;AAChBA,cAAQ,oBAAIC,IAAG;IAClB;EACL;AAEA,SAAO;IACHL,KAAG,SAAA,IAACQ,KAAG;AACH,UAAIC,QAAQL,MAAMJ,IAAIQ,GAAG;AAEzB,UAAIC,UAAUR,QAAW;AACrB,eAAOQ;MACV;AACD,WAAKA,QAAQH,cAAcN,IAAIQ,GAAG,OAAOP,QAAW;AAChDM,eAAOC,KAAKC,KAAK;AACjB,eAAOA;MACV;;IAELP,KAAIM,SAAAA,IAAAA,KAAKC,OAAK;AACV,UAAIL,MAAMM,IAAIF,GAAG,GAAG;AAChBJ,cAAMF,IAAIM,KAAKC,KAAK;MACvB,OAAM;AACHF,eAAOC,KAAKC,KAAK;MACpB;IACL;;AAER;;;ACjDO,IAAME,qBAAqB;AAE5B,SAAUC,qBAAqBC,QAAc;AAC/C,MAAMC,YAAYD,OAAOC,aAAa;AACtC,MAAMC,6BAA6BD,UAAUE,WAAW;AACxD,MAAMC,0BAA0BH,UAAU,CAAC;AAC3C,MAAMI,kBAAkBJ,UAAUE;AAGlC,SAAO,SAASG,eAAeC,WAAiB;AAC5C,QAAMC,YAAY,CAAA;AAElB,QAAIC,eAAe;AACnB,QAAIC,gBAAgB;AACpB,QAAIC;AAEJ,aAASC,QAAQ,GAAGA,QAAQL,UAAUJ,QAAQS,SAAS;AACnD,UAAIC,mBAAmBN,UAAUK,KAAK;AAEtC,UAAIH,iBAAiB,GAAG;AACpB,YACII,qBAAqBT,4BACpBF,8BACGK,UAAUO,MAAMF,OAAOA,QAAQP,eAAe,MAAMJ,YAC1D;AACEO,oBAAUO,KAAKR,UAAUO,MAAMJ,eAAeE,KAAK,CAAC;AACpDF,0BAAgBE,QAAQP;AACxB;QACH;AAED,YAAIQ,qBAAqB,KAAK;AAC1BF,oCAA0BC;AAC1B;QACH;MACJ;AAED,UAAIC,qBAAqB,KAAK;AAC1BJ;MACH,WAAUI,qBAAqB,KAAK;AACjCJ;MACH;IACJ;AAED,QAAMO,qCACFR,UAAUL,WAAW,IAAII,YAAYA,UAAUU,UAAUP,aAAa;AAC1E,QAAMQ,uBACFF,mCAAmCG,WAAWrB,kBAAkB;AACpE,QAAMsB,gBAAgBF,uBAChBF,mCAAmCC,UAAU,CAAC,IAC9CD;AAEN,QAAMK,+BACFV,2BAA2BA,0BAA0BD,gBAC/CC,0BAA0BD,gBAC1BY;AAEV,WAAO;MACHd;MACAU;MACAE;MACAC;;;AAGZ;AAOM,SAAUE,cAAcf,WAAmB;AAC7C,MAAIA,UAAUL,UAAU,GAAG;AACvB,WAAOK;EACV;AAED,MAAMgB,kBAA4B,CAAA;AAClC,MAAIC,oBAA8B,CAAA;AAElCjB,YAAUkB,QAAQ,SAACC,UAAY;AAC3B,QAAMC,qBAAqBD,SAAS,CAAC,MAAM;AAE3C,QAAIC,oBAAoB;AACpBJ,sBAAgBT,KAAI,MAApBS,iBAAwBC,kBAAkBI,KAAI,EAAIF,OAAAA,CAAAA,QAAQ,CAAC,CAAA;AAC3DF,0BAAoB,CAAA;IACvB,OAAM;AACHA,wBAAkBV,KAAKY,QAAQ;IAClC;EACL,CAAC;AAEDH,kBAAgBT,KAAhBS,MAAAA,iBAAwBC,kBAAkBI,KAAI,CAAE;AAEhD,SAAOL;AACX;;;ACvFM,SAAUM,kBAAkBC,QAAc;AAC5C,SAAO;IACHC,OAAOC,eAA+BF,OAAOG,SAAS;IACtDC,gBAAgBC,qBAAqBL,MAAM;IAC3C,GAAGM,iBAAiBN,MAAM;;AAElC;;;ACVA,IAAMO,sBAAsB;AAEZ,SAAAC,eAAeC,WAAmBC,aAAwB;AACtE,MAAQC,iBAAiED,YAAjEC,gBAAgBC,kBAAiDF,YAAjDE,iBAAiBC,8BAAgCH,YAAhCG;AASzC,MAAMC,wBAAwB,oBAAIC,IAAG;AAErC,SACIN,UACKO,KAAI,EACJC,MAAMV,mBAAmB,EACzBW,IAAI,SAACC,mBAAqB;AACvB,QAKIR,kBAAAA,eAAeQ,iBAAiB,GAJhCC,YAAS,gBAATA,WACAC,uBAAoB,gBAApBA,sBACAC,gBAAa,gBAAbA,eACAC,+BAA4B,gBAA5BA;AAGJ,QAAIC,eAAeZ,gBACfW,+BACMD,cAAcG,UAAU,GAAGF,4BAA4B,IACvDD,aAAa;AAGvB,QAAII,qBAAqBC,QAAQJ,4BAA4B;AAE7D,QAAI,CAACC,cAAc;AACf,UAAI,CAACD,8BAA8B;AAC/B,eAAO;UACHK,iBAAiB;UACjBT;;MAEP;AAEDK,qBAAeZ,gBAAgBU,aAAa;AAE5C,UAAI,CAACE,cAAc;AACf,eAAO;UACHI,iBAAiB;UACjBT;;MAEP;AAEDO,2BAAqB;IACxB;AAED,QAAMG,kBAAkBC,cAAcV,SAAS,EAAEW,KAAK,GAAG;AAEzD,QAAMC,aAAaX,uBACbQ,kBAAkBI,qBAClBJ;AAEN,WAAO;MACHD,iBAAiB;MACjBI;MACAR;MACAL;MACAO;;GAEP,EACAQ,QAAO,EAEPC,OAAO,SAACC,QAAU;AACf,QAAI,CAACA,OAAOR,iBAAiB;AACzB,aAAO;IACV;AAED,QAAQI,aAAiDI,OAAjDJ,YAAYR,eAAqCY,OAArCZ,cAAcE,qBAAuBU,OAAvBV;AAElC,QAAMW,UAAUL,aAAaR;AAE7B,QAAIV,sBAAsBwB,IAAID,OAAO,GAAG;AACpC,aAAO;IACV;AAEDvB,0BAAsByB,IAAIF,OAAO;AAEjCxB,gCAA4BW,cAAcE,kBAAkB,EAAEc,QAAQ,SAACC,OAAK;AAAA,aACxE3B,sBAAsByB,IAAIP,aAAaS,KAAK;KAC/C;AAED,WAAO;GACV,EACAP,QAAO,EACPhB,IAAI,SAACkB,QAAM;AAAA,WAAKA,OAAOjB;EAAiB,CAAA,EACxCY,KAAK,GAAG;AAErB;;;ACxFgB,SAAAW,sBACqD;AAAA,WAAA,OAAA,UAAA,QAA9DC,eAA8D,IAAA,MAAA,IAAA,GAAA,OAAA,GAAA,OAAA,MAAA,QAAA;AAA9DA,iBAA8D,IAAA,IAAA,UAAA,IAAA;EAAA;AAEjE,MAAIC;AACJ,MAAIC;AACJ,MAAIC;AACJ,MAAIC,iBAAiBC;AAErB,WAASA,kBAAkBC,WAAiB;AACxC,QAAOC,oBAA0CP,aAAY,CAAA,GAAhCQ,mBAAoBR,aAAY,MAAA,CAAA;AAE7D,QAAMS,SAASD,iBAAiBE,OAC5B,SAACC,gBAAgBC,qBAAmB;AAAA,aAAKA,oBAAoBD,cAAc;OAC3EJ,kBAAiB,CAAE;AAGvBN,kBAAcY,kBAAkBJ,MAAM;AACtCP,eAAWD,YAAYa,MAAMC;AAC7BZ,eAAWF,YAAYa,MAAME;AAC7BZ,qBAAiBa;AAEjB,WAAOA,cAAcX,SAAS;EAClC;AAEA,WAASW,cAAcX,WAAiB;AACpC,QAAMY,eAAehB,SAASI,SAAS;AAEvC,QAAIY,cAAc;AACd,aAAOA;IACV;AAED,QAAMC,SAASC,eAAed,WAAWL,WAAW;AACpDE,aAASG,WAAWa,MAAM;AAE1B,WAAOA;EACX;AAEA,SAAO,SAASE,oBAAiB;AAC7B,WAAOjB,eAAekB,OAAOC,MAAM,MAAMC,SAAgB,CAAC;;AAElE;;;AChDM,SAAUC,UAAUC,KAAW;AACjC,MAAMC,cAAc,SAAdA,aAAeC,OAAkB;AAAA,WAAKA,MAAMF,GAAG,KAAK,CAAA;EAAE;AAE5DC,cAAYE,gBAAgB;AAE5B,SAAOF;AACX;;;ACRA;;;;;;;;;;;;;;;;;IAAMG,sBAAsB;AAC5B,IAAMC,gBAAgB;AACtB,IAAMC,gBAAgB,oBAAIC,IAAI,CAAC,MAAM,QAAQ,QAAQ,CAAC;AACtD,IAAMC,kBAAkB;AACxB,IAAMC,kBACF;AAEJ,IAAMC,cAAc;AAEd,SAAUC,SAASC,OAAa;AAClC,SACIC,SAASD,KAAK,KACdN,cAAcQ,IAAIF,KAAK,KACvBP,cAAcU,KAAKH,KAAK,KACxBI,kBAAkBJ,KAAK;AAE/B;AAEM,SAAUI,kBAAkBJ,OAAa;AAC3C,SAAOK,oBAAoBL,OAAO,UAAUM,YAAY;AAC5D;AAEM,SAAUC,gBAAgBP,OAAa;AACzC,SAAOK,oBAAoBL,OAAO,QAAQQ,OAAO;AACrD;AAEM,SAAUC,oBAAoBT,OAAa;AAC7C,SAAOK,oBAAoBL,OAAO,YAAYQ,OAAO;AACzD;AAEM,SAAUE,eAAeV,OAAa;AACxC,SAAOK,oBAAoBL,OAAO,OAAOW,KAAK;AAClD;AAEM,SAAUC,kBAAkBZ,OAAa;AAC3C,SAAOK,oBAAoBL,OAAO,UAAUC,QAAQ;AACxD;AAKO,IAAMY,oBAAoBD;AAE3B,SAAUX,SAASD,OAAa;AAClC,SAAO,CAACc,OAAOC,MAAMD,OAAOd,KAAK,CAAC;AACtC;AAEM,SAAUgB,UAAUhB,OAAa;AACnC,SAAOA,MAAMiB,SAAS,GAAG,KAAKhB,SAASD,MAAMkB,MAAM,GAAG,EAAE,CAAC;AAC7D;AAEM,SAAUC,UAAUnB,OAAa;AACnC,SAAOoB,cAAcpB,KAAK,KAAKK,oBAAoBL,OAAO,UAAUoB,aAAa;AACrF;AAEM,SAAUC,iBAAiBrB,OAAa;AAC1C,SAAOR,oBAAoBW,KAAKH,KAAK;AACzC;SAEgBsB,QAAK;AACjB,SAAO;AACX;AAEM,SAAUC,aAAavB,OAAa;AACtC,SAAOJ,gBAAgBO,KAAKH,KAAK;AACrC;AAEM,SAAUwB,kBAAkBxB,OAAa;AAC3C,SAAOK,oBAAoBL,OAAO,IAAIyB,QAAQ;AAClD;AAEA,SAASpB,oBAAoBL,OAAe0B,OAAeC,WAAqC;AAC5F,MAAMC,SAASpC,oBAAoBqC,KAAK7B,KAAK;AAE7C,MAAI4B,QAAQ;AACR,QAAIA,OAAO,CAAC,GAAG;AACX,aAAOA,OAAO,CAAC,MAAMF;IACxB;AAED,WAAOC,UAAUC,OAAO,CAAC,CAAE;EAC9B;AAED,SAAO;AACX;AAEA,SAAStB,aAAaN,OAAa;AAC/B,SAAOH,gBAAgBM,KAAKH,KAAK;AACrC;AAEA,SAASQ,UAAO;AACZ,SAAO;AACX;AAEA,SAASG,MAAMX,OAAa;AACxB,SAAOA,MAAM8B,WAAW,MAAM;AAClC;AAEA,SAASV,cAAcpB,OAAa;AAChC,SAAOc,OAAOK,UAAUL,OAAOd,KAAK,CAAC;AACzC;AAEA,SAASyB,SAASzB,OAAa;AAC3B,SAAOF,YAAYK,KAAKH,KAAK;AACjC;;;SCrFgB+B,mBAAgB;AAC5B,MAAMC,SAASC,UAAU,QAAQ;AACjC,MAAMC,UAAUD,UAAU,SAAS;AACnC,MAAME,OAAOF,UAAU,MAAM;AAC7B,MAAMG,aAAaH,UAAU,YAAY;AACzC,MAAMI,cAAcJ,UAAU,aAAa;AAC3C,MAAMK,eAAeL,UAAU,cAAc;AAC7C,MAAMM,gBAAgBN,UAAU,eAAe;AAC/C,MAAMO,cAAcP,UAAU,aAAa;AAC3C,MAAMQ,WAAWR,UAAU,UAAU;AACrC,MAAMS,YAAYT,UAAU,WAAW;AACvC,MAAMU,YAAYV,UAAU,WAAW;AACvC,MAAMW,SAASX,UAAU,QAAQ;AACjC,MAAMY,MAAMZ,UAAU,KAAK;AAC3B,MAAMa,qBAAqBb,UAAU,oBAAoB;AACzD,MAAMc,6BAA6Bd,UAAU,4BAA4B;AACzE,MAAMe,QAAQf,UAAU,OAAO;AAC/B,MAAMgB,SAAShB,UAAU,QAAQ;AACjC,MAAMiB,UAAUjB,UAAU,SAAS;AACnC,MAAMkB,UAAUlB,UAAU,SAAS;AACnC,MAAMmB,WAAWnB,UAAU,UAAU;AACrC,MAAMoB,QAAQpB,UAAU,OAAO;AAC/B,MAAMqB,QAAQrB,UAAU,OAAO;AAC/B,MAAMsB,OAAOtB,UAAU,MAAM;AAC7B,MAAMuB,QAAQvB,UAAU,OAAO;AAC/B,MAAMwB,YAAYxB,UAAU,WAAW;AAEvC,MAAMyB,gBAAgB,SAAhBA,iBAAa;AAAA,WAAS,CAAC,QAAQ,WAAW,MAAM;EAAU;AAChE,MAAMC,cAAc,SAAdA,eAAW;AAAA,WAAS,CAAC,QAAQ,UAAU,QAAQ,WAAW,QAAQ;EAAU;AAClF,MAAMC,iCAAiC,SAAjCA,kCAA8B;AAAA,WAAS,CAAC,QAAQC,kBAAkB3B,OAAO;EAAU;AACzF,MAAM4B,0BAA0B,SAA1BA,2BAAuB;AAAA,WAAS,CAACD,kBAAkB3B,OAAO;EAAU;AAC1E,MAAM6B,qBAAqB,SAArBA,sBAAkB;AAAA,WAAS,CAAC,IAAIC,QAAQ;EAAU;AACxD,MAAMC,gCAAgC,SAAhCA,iCAA6B;AAAA,WAAS,CAAC,QAAQC,UAAUL,gBAAgB;EAAU;AACzF,MAAMM,eAAe,SAAfA,gBAAY;AAAA,WACd,CACI,UACA,UACA,QACA,eACA,YACA,SACA,gBACA,aACA,KAAK;EACC;AACd,MAAMC,gBAAgB,SAAhBA,iBAAa;AAAA,WAAS,CAAC,SAAS,UAAU,UAAU,UAAU,MAAM;EAAU;AACpF,MAAMC,gBAAgB,SAAhBA,iBAAa;AAAA,WACf,CACI,UACA,YACA,UACA,WACA,UACA,WACA,eACA,cACA,cACA,cACA,cACA,aACA,OACA,cACA,SACA,cACA,cAAc;EACR;AACd,MAAMC,WAAW,SAAXA,YAAQ;AAAA,WACV,CAAC,SAAS,OAAO,UAAU,WAAW,UAAU,UAAU,SAAS;EAAU;AACjF,MAAMC,kBAAkB,SAAlBA,mBAAe;AAAA,WAAS,CAAC,IAAI,KAAKV,gBAAgB;EAAU;AAClE,MAAMW,YAAY,SAAZA,aAAS;AAAA,WACX,CAAC,QAAQ,SAAS,OAAO,cAAc,QAAQ,QAAQ,SAAS,QAAQ;EAAU;AACtF,MAAMC,YAAY,SAAZA,aAAS;AAAA,WAAS,CAACP,UAAUQ,iBAAiB;EAAC;AACrD,MAAMC,wBAAwB,SAAxBA,yBAAqB;AAAA,WAAS,CAACT,UAAUL,gBAAgB;EAAC;AAEhE,SAAO;IACHe,WAAW;IACXC,OAAO;MACH7C,QAAQ,CAAC8C,KAAK;MACd5C,SAAS,CAAC8B,QAAQ;MAClB7B,MAAM,CAAC,QAAQ,IAAI4C,cAAclB,gBAAgB;MACjDzB,YAAYqC,UAAS;MACrBpC,aAAa,CAACL,MAAM;MACpBM,cAAc,CAAC,QAAQ,IAAI,QAAQyC,cAAclB,gBAAgB;MACjEtB,eAAeuB,wBAAuB;MACtCtB,aAAauB,mBAAkB;MAC/BtB,UAAUgC,UAAS;MACnB/B,WAAW6B,gBAAe;MAC1B5B,WAAWgC,sBAAqB;MAChC/B,QAAQ2B,gBAAe;MACvB1B,KAAKiB,wBAAuB;MAC5BhB,oBAAoB,CAACd,MAAM;MAC3Be,4BAA4B,CAACiC,WAAWC,iBAAiB;MACzDjC,OAAOY,+BAA8B;MACrCX,QAAQW,+BAA8B;MACtCV,SAASuB,UAAS;MAClBtB,SAASW,wBAAuB;MAChCV,UAAUqB,UAAS;MACnBpB,OAAOoB,UAAS;MAChBnB,OAAOiB,gBAAe;MACtBhB,MAAMoB,sBAAqB;MAC3BnB,OAAOM,wBAAuB;MAC9BL,WAAWK,wBAAuB;;IAEtCoB,aAAa;;;;;;MAMTC,QAAQ,CAAC;QAAEA,QAAQ,CAAC,QAAQ,UAAU,SAAStB,gBAAgB;OAAG;;;;;MAKlEuB,WAAW,CAAC,WAAW;;;;;MAKvBC,SAAS,CAAC;QAAEA,SAAS,CAACN,YAAY;MAAC,CAAE;;;;;MAKrC,eAAe,CAAC;QAAE,eAAeP,UAAS;OAAI;;;;;MAK9C,gBAAgB,CAAC;QAAE,gBAAgBA,UAAS;OAAI;;;;;MAKhD,gBAAgB,CAAC;QAAE,gBAAgB,CAAC,QAAQ,SAAS,cAAc,cAAc;OAAG;;;;;MAKpF,kBAAkB,CAAC;QAAE,kBAAkB,CAAC,SAAS,OAAO;MAAC,CAAE;;;;;MAK3Dc,KAAK,CAAC;QAAEA,KAAK,CAAC,UAAU,SAAS;MAAC,CAAE;;;;;MAKpCC,SAAS,CACL,SACA,gBACA,UACA,QACA,eACA,SACA,gBACA,iBACA,cACA,gBACA,sBACA,sBACA,sBACA,mBACA,aACA,aACA,QACA,eACA,YACA,aACA,QAAQ;;;;;MAMZ,SAAO,CAAC;QAAE,SAAO,CAAC,SAAS,QAAQ,MAAM;OAAG;;;;;MAK5CC,OAAO,CAAC;QAAEA,OAAO,CAAC,QAAQ,SAAS,QAAQ,MAAM;OAAG;;;;;MAKpDC,WAAW,CAAC,WAAW,gBAAgB;;;;;MAKvC,cAAc,CAAC;QAAEC,QAAQ,CAAC,WAAW,SAAS,QAAQ,QAAQ,YAAY;OAAG;;;;;MAK7E,mBAAmB,CAAC;QAAEA,QAAYvB,CAAAA,EAAAA,OAAAA,aAAY,GAAE,CAAEN,gBAAgB,CAAA;OAAG;;;;;MAKrE8B,UAAU,CAAC;QAAEA,UAAUhC,YAAW;OAAI;;;;;MAKtC,cAAc,CAAC;QAAE,cAAcA,YAAW;OAAI;;;;;MAK9C,cAAc,CAAC;QAAE,cAAcA,YAAW;OAAI;;;;;MAK9CiC,YAAY,CAAC;QAAEA,YAAYlC,cAAa;OAAI;;;;;MAK5C,gBAAgB,CAAC;QAAE,gBAAgBA,cAAa;OAAI;;;;;MAKpD,gBAAgB,CAAC;QAAE,gBAAgBA,cAAa;OAAI;;;;;MAKpDmC,UAAU,CAAC,UAAU,SAAS,YAAY,YAAY,QAAQ;;;;;MAK9D7C,OAAO,CAAC;QAAEA,OAAO,CAACA,KAAK;MAAC,CAAE;;;;;MAK1B,WAAW,CAAC;QAAE,WAAW,CAACA,KAAK;MAAC,CAAE;;;;;MAKlC,WAAW,CAAC;QAAE,WAAW,CAACA,KAAK;MAAC,CAAE;;;;;MAKlC8C,OAAO,CAAC;QAAEA,OAAO,CAAC9C,KAAK;MAAC,CAAE;;;;;MAK1B+C,KAAK,CAAC;QAAEA,KAAK,CAAC/C,KAAK;MAAC,CAAE;;;;;MAKtBgD,KAAK,CAAC;QAAEA,KAAK,CAAChD,KAAK;MAAC,CAAE;;;;;MAKtBiD,OAAO,CAAC;QAAEA,OAAO,CAACjD,KAAK;MAAC,CAAE;;;;;MAK1BkD,QAAQ,CAAC;QAAEA,QAAQ,CAAClD,KAAK;MAAC,CAAE;;;;;MAK5BmD,MAAM,CAAC;QAAEA,MAAM,CAACnD,KAAK;MAAC,CAAE;;;;;MAKxBoD,YAAY,CAAC,WAAW,aAAa,UAAU;;;;;MAK/CC,GAAG,CAAC;QAAEA,GAAG,CAAC,QAAQC,SAAS;MAAC,CAAE;;;;;;MAM9BC,OAAO,CAAC;QAAEA,OAAO3C,+BAA8B;OAAI;;;;;MAKnD,kBAAkB,CAAC;QAAE4C,MAAM,CAAC,OAAO,eAAe,OAAO,aAAa;OAAG;;;;;MAKzE,aAAa,CAAC;QAAEA,MAAM,CAAC,QAAQ,gBAAgB,QAAQ;OAAG;;;;;MAK1DA,MAAM,CAAC;QAAEA,MAAM,CAAC,KAAK,QAAQ,WAAW,QAAQ3C,gBAAgB;OAAG;;;;;MAKnE4C,MAAM,CAAC;QAAEA,MAAMlC,gBAAe;OAAI;;;;;MAKlCmC,QAAQ,CAAC;QAAEA,QAAQnC,gBAAe;OAAI;;;;;MAKtCoC,OAAO,CAAC;QAAEA,OAAO,CAAC,SAAS,QAAQ,QAAQL,SAAS;OAAG;;;;;MAKvD,aAAa,CAAC;QAAE,aAAa,CAACxB,KAAK;MAAC,CAAE;;;;;MAKtC,iBAAiB,CAAC;QAAE8B,KAAK,CAAC,QAAQ;UAAEC,MAAM,CAAC,QAAQP,SAAS;WAAKzC,gBAAgB;MAAC,CAAE;;;;;MAKpF,aAAa,CAAC;QAAE,aAAaI,8BAA6B;OAAI;;;;;MAK9D,WAAW,CAAC;QAAE,WAAWA,8BAA6B;OAAI;;;;;MAK1D,aAAa,CAAC;QAAE,aAAa,CAACa,KAAK;MAAC,CAAE;;;;;MAKtC,iBAAiB,CAAC;QAAEgC,KAAK,CAAC,QAAQ;UAAED,MAAM,CAACP,SAAS;QAAC,GAAIzC,gBAAgB;OAAG;;;;;MAK5E,aAAa,CAAC;QAAE,aAAaI,8BAA6B;OAAI;;;;;MAK9D,WAAW,CAAC;QAAE,WAAWA,8BAA6B;OAAI;;;;;MAK1D,aAAa,CAAC;QAAE,aAAa,CAAC,OAAO,OAAO,SAAS,aAAa,WAAW;OAAG;;;;;MAKhF,aAAa,CAAC;QAAE,aAAa,CAAC,QAAQ,OAAO,OAAO,MAAMJ,gBAAgB;OAAG;;;;;MAK7E,aAAa,CAAC;QAAE,aAAa,CAAC,QAAQ,OAAO,OAAO,MAAMA,gBAAgB;OAAG;;;;;MAK7EhB,KAAK,CAAC;QAAEA,KAAK,CAACA,GAAG;MAAC,CAAE;;;;;MAKpB,SAAS,CAAC;QAAE,SAAS,CAACA,GAAG;MAAC,CAAE;;;;;MAK5B,SAAS,CAAC;QAAE,SAAS,CAACA,GAAG;MAAC,CAAE;;;;;MAK5B,mBAAmB,CAAC;QAAEkE,SAAU,CAAA,QAAQ,EAAKzC,OAAAA,SAAQ,CAAE;OAAG;;;;;MAK1D,iBAAiB,CAAC;QAAE,iBAAiB,CAAC,SAAS,OAAO,UAAU,SAAS;OAAG;;;;;MAK5E,gBAAgB,CAAC;QAAE,gBAAgB,CAAC,QAAQ,SAAS,OAAO,UAAU,SAAS;OAAG;;;;;MAKlF,iBAAiB,CAAC;QAAE0C,SAAO,CAAG,QAAQ,EAAA,OAAK1C,SAAQ,GAAE,CAAE,UAAU,CAAA;OAAG;;;;;MAKpE,eAAe,CAAC;QAAE2C,OAAO,CAAC,SAAS,OAAO,UAAU,YAAY,SAAS;OAAG;;;;;MAK5E,cAAc,CAAC;QAAEC,MAAM,CAAC,QAAQ,SAAS,OAAO,UAAU,WAAW,UAAU;OAAG;;;;;MAKlF,iBAAiB,CAAC;QAAE,iBAAqB5C,CAAAA,EAAAA,OAAAA,SAAQ,GAAE,CAAE,UAAU,CAAA;OAAG;;;;;MAKlE,eAAe,CAAC;QAAE,eAAe,CAAC,SAAS,OAAO,UAAU,YAAY,SAAS;OAAG;;;;;MAKpF,cAAc,CAAC;QAAE,cAAc,CAAC,QAAQ,SAAS,OAAO,UAAU,SAAS;OAAG;;;;;;MAM9E6C,GAAG,CAAC;QAAEA,GAAG,CAAChE,OAAO;MAAC,CAAE;;;;;MAKpBiE,IAAI,CAAC;QAAEA,IAAI,CAACjE,OAAO;MAAC,CAAE;;;;;MAKtBkE,IAAI,CAAC;QAAEA,IAAI,CAAClE,OAAO;MAAC,CAAE;;;;;MAKtBmE,IAAI,CAAC;QAAEA,IAAI,CAACnE,OAAO;MAAC,CAAE;;;;;MAKtBoE,IAAI,CAAC;QAAEA,IAAI,CAACpE,OAAO;MAAC,CAAE;;;;;MAKtBqE,IAAI,CAAC;QAAEA,IAAI,CAACrE,OAAO;MAAC,CAAE;;;;;MAKtBsE,IAAI,CAAC;QAAEA,IAAI,CAACtE,OAAO;MAAC,CAAE;;;;;MAKtBuE,IAAI,CAAC;QAAEA,IAAI,CAACvE,OAAO;MAAC,CAAE;;;;;MAKtBwE,IAAI,CAAC;QAAEA,IAAI,CAACxE,OAAO;MAAC,CAAE;;;;;MAKtByE,GAAG,CAAC;QAAEA,GAAG,CAAC3E,MAAM;MAAC,CAAE;;;;;MAKnB4E,IAAI,CAAC;QAAEA,IAAI,CAAC5E,MAAM;MAAC,CAAE;;;;;MAKrB6E,IAAI,CAAC;QAAEA,IAAI,CAAC7E,MAAM;MAAC,CAAE;;;;;MAKrB8E,IAAI,CAAC;QAAEA,IAAI,CAAC9E,MAAM;MAAC,CAAE;;;;;MAKrB+E,IAAI,CAAC;QAAEA,IAAI,CAAC/E,MAAM;MAAC,CAAE;;;;;MAKrBgF,IAAI,CAAC;QAAEA,IAAI,CAAChF,MAAM;MAAC,CAAE;;;;;MAKrBiF,IAAI,CAAC;QAAEA,IAAI,CAACjF,MAAM;MAAC,CAAE;;;;;MAKrBkF,IAAI,CAAC;QAAEA,IAAI,CAAClF,MAAM;MAAC,CAAE;;;;;MAKrBmF,IAAI,CAAC;QAAEA,IAAI,CAACnF,MAAM;MAAC,CAAE;;;;;MAKrB,WAAW,CAAC;QAAE,WAAW,CAACO,KAAK;MAAC,CAAE;;;;;MAKlC,mBAAmB,CAAC,iBAAiB;;;;;MAKrC,WAAW,CAAC;QAAE,WAAW,CAACA,KAAK;MAAC,CAAE;;;;;MAKlC,mBAAmB,CAAC,iBAAiB;;;;;;MAMrC6E,GAAG,CAAC;QAAEA,GAAG,CAAC,QAAQ,OAAO,OAAO,OAAOxE,kBAAkB3B,OAAO;OAAG;;;;;MAKnE,SAAS,CAAC;QAAE,SAAS,CAAC,OAAO,OAAO,OAAO2B,kBAAkBG,QAAQ;OAAG;;;;;MAKxE,SAAS,CACL;QACI,SAAS,CACL,KACA,QACA,QACA,OACA,OACA,OACA,SACA;UAAEsE,QAAQ,CAACvD,YAAY;WACvBA,cACAlB,gBAAgB;MAEvB,CAAA;;;;;MAML0E,GAAG,CAAC;QAAEA,GAAG,CAAC1E,kBAAkB3B,SAAS,QAAQ,OAAO,OAAO,KAAK;OAAG;;;;;MAKnE,SAAS,CAAC;QAAE,SAAS,CAAC,OAAO,OAAO,OAAO2B,kBAAkBG,QAAQ;OAAG;;;;;MAKxE,SAAS,CAAC;QAAE,SAAS,CAACH,kBAAkB3B,SAAS,OAAO,OAAO,KAAK;OAAG;;;;;;MAMvE,aAAa,CAAC;QAAEsG,MAAM,CAAC,QAAQzD,cAAcE,iBAAiB;OAAG;;;;;MAKjE,kBAAkB,CAAC,eAAe,sBAAsB;;;;;MAKxD,cAAc,CAAC,UAAU,YAAY;;;;;MAKrC,eAAe,CACX;QACIwD,MAAM,CACF,QACA,cACA,SACA,UACA,UACA,YACA,QACA,aACA,SACA/D,iBAAiB;MAExB,CAAA;;;;;MAML,eAAe,CAAC;QAAE+D,MAAM,CAAC3D,KAAK;MAAC,CAAE;;;;;MAKjC,cAAc,CAAC,aAAa;;;;;MAK5B,eAAe,CAAC,SAAS;;;;;MAKzB,oBAAoB,CAAC,cAAc;;;;;MAKnC,cAAc,CAAC,eAAe,eAAe;;;;;MAK7C,eAAe,CAAC,qBAAqB,cAAc;;;;;MAKnD,gBAAgB,CAAC,sBAAsB,kBAAkB;;;;;MAKzD4D,UAAU,CACN;QACIA,UAAU,CACN,WACA,SACA,UACA,QACA,SACA,UACA7E,gBAAgB;MAEvB,CAAA;;;;;MAML,cAAc,CAAC;QAAE,cAAc,CAAC,QAAQK,UAAUQ,iBAAiB;OAAG;;;;;MAKtEiE,SAAS,CACL;QACIA,SAAS,CACL,QACA,SACA,QACA,UACA,WACA,SACA9E,kBACAG,QAAQ;MAEf,CAAA;;;;;MAML,cAAc,CAAC;QAAE,cAAc,CAAC,QAAQH,gBAAgB;MAAC,CAAE;;;;;MAK3D,mBAAmB,CAAC;QAAE+E,MAAM,CAAC,QAAQ,QAAQ,WAAW/E,gBAAgB;OAAG;;;;;MAK3E,uBAAuB,CAAC;QAAE+E,MAAM,CAAC,UAAU,SAAS;MAAC,CAAE;;;;;;MAMvD,qBAAqB,CAAC;QAAEC,aAAa,CAAC7G,MAAM;MAAC,CAAE;;;;;MAK/C,uBAAuB,CAAC;QAAE,uBAAuB,CAACkB,OAAO;MAAC,CAAE;;;;;MAK5D,kBAAkB,CAAC;QAAEsF,MAAM,CAAC,QAAQ,UAAU,SAAS,WAAW,SAAS,KAAK;OAAG;;;;;MAKnF,cAAc,CAAC;QAAEA,MAAM,CAACxG,MAAM;MAAC,CAAE;;;;;MAKjC,gBAAgB,CAAC;QAAE,gBAAgB,CAACkB,OAAO;MAAC,CAAE;;;;;MAK9C,mBAAmB,CAAC,aAAa,YAAY,gBAAgB,cAAc;;;;;MAK3E,yBAAyB,CAAC;QAAE4F,YAAgB1E,CAAAA,EAAAA,OAAAA,cAAa,GAAE,CAAE,MAAM,CAAA;OAAG;;;;;MAKtE,6BAA6B,CAAC;QAAE0E,YAAY,CAAC,QAAQ,aAAa9E,QAAQ;OAAG;;;;;MAK7E,oBAAoB,CAAC;QAAE,oBAAoB,CAAC,QAAQH,kBAAkBG,QAAQ;OAAG;;;;;MAKjF,yBAAyB,CAAC;QAAE8E,YAAY,CAAC9G,MAAM;MAAC,CAAE;;;;;MAKlD,kBAAkB,CAAC,aAAa,aAAa,cAAc,aAAa;;;;;MAKxE,iBAAiB,CAAC,YAAY,iBAAiB,WAAW;;;;;MAK1D+G,QAAQ,CAAC;QAAEA,QAAQjF,wBAAuB;OAAI;;;;;MAK9C,kBAAkB,CACd;QACIkF,OAAO,CACH,YACA,OACA,UACA,UACA,YACA,eACA,OACA,SACAnF,gBAAgB;MAEvB,CAAA;;;;;MAMLoF,YAAY,CACR;QAAEA,YAAY,CAAC,UAAU,UAAU,OAAO,YAAY,YAAY,cAAc;MAAG,CAAA;;;;;MAMvF,SAAO,CAAC;QAAE,SAAO,CAAC,UAAU,SAAS,OAAO,MAAM;OAAG;;;;;MAKrDC,SAAS,CAAC;QAAEA,SAAS,CAAC,QAAQ,UAAU,MAAM;OAAG;;;;;MAKjDlC,SAAS,CAAC;QAAEA,SAAS,CAAC,QAAQnD,gBAAgB;MAAC,CAAE;;;;;;MAMjD,iBAAiB,CAAC;QAAEsF,IAAI,CAAC,SAAS,SAAS,QAAQ;OAAG;;;;;MAKtD,WAAW,CAAC;QAAE,WAAW,CAAC,UAAU,WAAW,WAAW,MAAM;OAAG;;;;;;MAMnE,cAAc,CAAC;QAAE,cAAc,CAACjG,OAAO;MAAC,CAAE;;;;;MAK1C,aAAa,CAAC;QAAE,aAAa,CAAC,UAAU,WAAW,SAAS;OAAG;;;;;MAK/D,eAAe,CAAC;QAAEiG,IAAQhF,CAAAA,EAAAA,OAAAA,aAAY,GAAE,CAAEiF,mBAAmB,CAAA;OAAG;;;;;MAKhE,aAAa,CAAC;QAAED,IAAI,CAAC,aAAa;UAAEE,QAAQ,CAAC,IAAI,KAAK,KAAK,SAAS,OAAO;SAAG;MAAC,CAAE;;;;;MAKjF,WAAW,CAAC;QAAEF,IAAI,CAAC,QAAQ,SAAS,WAAWG,eAAe;OAAG;;;;;MAKjE,YAAY,CACR;QACIH,IAAI,CACA,QACA;UAAE,eAAe,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;QAAG,GAC/DI,cAAc;MAErB,CAAA;;;;;MAML,YAAY,CAAC;QAAEJ,IAAI,CAACnH,MAAM;MAAC,CAAE;;;;;MAK7B,qBAAqB,CAAC;QAAEwH,MAAM,CAACzG,0BAA0B;MAAC,CAAE;;;;;MAK5D,oBAAoB,CAAC;QAAE0G,KAAK,CAAC1G,0BAA0B;MAAC,CAAE;;;;;MAK1D,mBAAmB,CAAC;QAAE2G,IAAI,CAAC3G,0BAA0B;MAAC,CAAE;;;;;MAKxD,iBAAiB,CAAC;QAAEyG,MAAM,CAAC1G,kBAAkB;MAAC,CAAE;;;;;MAKhD,gBAAgB,CAAC;QAAE2G,KAAK,CAAC3G,kBAAkB;MAAC,CAAE;;;;;MAK9C,eAAe,CAAC;QAAE4G,IAAI,CAAC5G,kBAAkB;MAAC,CAAE;;;;;;MAM5C6G,SAAS,CAAC;QAAEA,SAAS,CAACrH,YAAY;MAAC,CAAE;;;;;MAKrC,aAAa,CAAC;QAAE,aAAa,CAACA,YAAY;MAAC,CAAE;;;;;MAK7C,aAAa,CAAC;QAAE,aAAa,CAACA,YAAY;MAAC,CAAE;;;;;MAK7C,aAAa,CAAC;QAAE,aAAa,CAACA,YAAY;MAAC,CAAE;;;;;MAK7C,aAAa,CAAC;QAAE,aAAa,CAACA,YAAY;MAAC,CAAE;;;;;MAK7C,aAAa,CAAC;QAAE,aAAa,CAACA,YAAY;MAAC,CAAE;;;;;MAK7C,aAAa,CAAC;QAAE,aAAa,CAACA,YAAY;MAAC,CAAE;;;;;MAK7C,cAAc,CAAC;QAAE,cAAc,CAACA,YAAY;MAAC,CAAE;;;;;MAK/C,cAAc,CAAC;QAAE,cAAc,CAACA,YAAY;MAAC,CAAE;;;;;MAK/C,cAAc,CAAC;QAAE,cAAc,CAACA,YAAY;MAAC,CAAE;;;;;MAK/C,cAAc,CAAC;QAAE,cAAc,CAACA,YAAY;MAAC,CAAE;;;;;MAK/C,cAAc,CAAC;QAAE,cAAc,CAACA,YAAY;MAAC,CAAE;;;;;MAK/C,cAAc,CAAC;QAAE,cAAc,CAACA,YAAY;MAAC,CAAE;;;;;MAK/C,cAAc,CAAC;QAAE,cAAc,CAACA,YAAY;MAAC,CAAE;;;;;MAK/C,cAAc,CAAC;QAAE,cAAc,CAACA,YAAY;MAAC,CAAE;;;;;MAK/C,YAAY,CAAC;QAAEsH,QAAQ,CAACpH,WAAW;MAAC,CAAE;;;;;MAKtC,cAAc,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAK5C,cAAc,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAK5C,cAAc,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAK5C,cAAc,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAK5C,cAAc,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAK5C,cAAc,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAK5C,cAAc,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAK5C,cAAc,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAK5C,kBAAkB,CAAC;QAAE,kBAAkB,CAACU,OAAO;MAAC,CAAE;;;;;MAKlD,gBAAgB,CAAC;QAAE0G,QAAYxF,CAAAA,EAAAA,OAAAA,cAAa,GAAE,CAAE,QAAQ,CAAA;OAAG;;;;;MAK3D,YAAY,CAAC;QAAE,YAAY,CAAC5B,WAAW;MAAC,CAAE;;;;;MAK1C,oBAAoB,CAAC,kBAAkB;;;;;MAKvC,YAAY,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAK1C,oBAAoB,CAAC,kBAAkB;;;;;MAKvC,kBAAkB,CAAC;QAAE,kBAAkB,CAACU,OAAO;MAAC,CAAE;;;;;MAKlD,gBAAgB,CAAC;QAAE2G,QAAQzF,cAAa;OAAI;;;;;MAK5C,gBAAgB,CAAC;QAAEwF,QAAQ,CAACvH,WAAW;MAAC,CAAE;;;;;MAK1C,kBAAkB,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAKhD,kBAAkB,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAKhD,kBAAkB,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAKhD,kBAAkB,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAKhD,kBAAkB,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAKhD,kBAAkB,CAAC;QAAE,YAAY,CAACA,WAAW;MAAC,CAAE;;;;;MAKhD,gBAAgB,CAAC;QAAEwH,QAAQ,CAACxH,WAAW;MAAC,CAAE;;;;;MAK1C,iBAAiB,CAAC;QAAEyH,SAAU,CAAA,EAAE,EAAK1F,OAAAA,cAAa,CAAE;OAAG;;;;;MAKvD,kBAAkB,CAAC;QAAE,kBAAkB,CAACP,kBAAkBG,QAAQ;MAAC,CAAE;;;;;MAKrE,aAAa,CAAC;QAAE8F,SAAS,CAAC9F,QAAQ;MAAC,CAAE;;;;;MAKrC,iBAAiB,CAAC;QAAE8F,SAAS,CAAC9H,MAAM;MAAC,CAAE;;;;;MAKvC,UAAU,CAAC;QAAE+H,MAAMhG,mBAAkB;OAAI;;;;;MAKzC,gBAAgB,CAAC,YAAY;;;;;MAK7B,cAAc,CAAC;QAAEgG,MAAM,CAAC/H,MAAM;MAAC,CAAE;;;;;MAKjC,gBAAgB,CAAC;QAAE,gBAAgB,CAACkB,OAAO;MAAC,CAAE;;;;;MAK9C,iBAAiB,CAAC;QAAE,eAAe,CAACc,QAAQ;MAAC,CAAE;;;;;MAK/C,qBAAqB,CAAC;QAAE,eAAe,CAAChC,MAAM;MAAC,CAAE;;;;;;MAMjDgI,QAAQ,CAAC;QAAEA,QAAQ,CAAC,IAAI,SAAS,QAAQjF,cAAckF,iBAAiB;OAAG;;;;;MAK3E,gBAAgB,CAAC;QAAED,QAAQ,CAAClF,KAAK;MAAC,CAAE;;;;;MAKpC5B,SAAS,CAAC;QAAEA,SAAS,CAACA,OAAO;MAAC,CAAE;;;;;MAKhC,aAAa,CAAC;QAAE,aAAamB,cAAa;OAAI;;;;;MAK9C,YAAY,CAAC;QAAE,YAAYA,cAAa;OAAI;;;;;;;MAO5C6F,QAAQ,CAAC;QAAEA,QAAQ,CAAC,IAAI,MAAM;MAAC,CAAE;;;;;MAKjC/H,MAAM,CAAC;QAAEA,MAAM,CAACA,IAAI;MAAC,CAAE;;;;;MAKvBC,YAAY,CAAC;QAAEA,YAAY,CAACA,UAAU;MAAC,CAAE;;;;;MAKzCK,UAAU,CAAC;QAAEA,UAAU,CAACA,QAAQ;MAAC,CAAE;;;;;MAKnC,eAAe,CAAC;QAAE,eAAe,CAAC,IAAI,QAAQsC,cAAclB,gBAAgB;OAAG;;;;;MAK/EnB,WAAW,CAAC;QAAEA,WAAW,CAACA,SAAS;MAAC,CAAE;;;;;MAKtC,cAAc,CAAC;QAAE,cAAc,CAACC,SAAS;MAAC,CAAE;;;;;MAK5CC,QAAQ,CAAC;QAAEA,QAAQ,CAACA,MAAM;MAAC,CAAE;;;;;MAK7BQ,UAAU,CAAC;QAAEA,UAAU,CAACA,QAAQ;MAAC,CAAE;;;;;MAKnCE,OAAO,CAAC;QAAEA,OAAO,CAACA,KAAK;MAAC,CAAE;;;;;;MAM1B,mBAAmB,CAAC;QAAE,mBAAmB,CAAC,IAAI,MAAM;MAAC,CAAE;;;;;MAKvD,iBAAiB,CAAC;QAAE,iBAAiB,CAACnB,IAAI;MAAC,CAAE;;;;;MAK7C,uBAAuB,CAAC;QAAE,uBAAuB,CAACC,UAAU;MAAC,CAAE;;;;;MAK/D,qBAAqB,CAAC;QAAE,qBAAqB,CAACK,QAAQ;MAAC,CAAE;;;;;MAKzD,sBAAsB,CAAC;QAAE,sBAAsB,CAACC,SAAS;MAAC,CAAE;;;;;MAK5D,uBAAuB,CAAC;QAAE,uBAAuB,CAACC,SAAS;MAAC,CAAE;;;;;MAK9D,mBAAmB,CAAC;QAAE,mBAAmB,CAACC,MAAM;MAAC,CAAE;;;;;MAKnD,oBAAoB,CAAC;QAAE,oBAAoB,CAACM,OAAO;MAAC,CAAE;;;;;MAKtD,qBAAqB,CAAC;QAAE,qBAAqB,CAACE,QAAQ;MAAC,CAAE;;;;;MAKzD,kBAAkB,CAAC;QAAE,kBAAkB,CAACE,KAAK;MAAC,CAAE;;;;;;MAMhD,mBAAmB,CAAC;QAAEsG,QAAQ,CAAC,YAAY,UAAU;MAAC,CAAE;;;;;MAKxD,kBAAkB,CAAC;QAAE,kBAAkB,CAACrH,aAAa;MAAC,CAAE;;;;;MAKxD,oBAAoB,CAAC;QAAE,oBAAoB,CAACA,aAAa;MAAC,CAAE;;;;;MAK5D,oBAAoB,CAAC;QAAE,oBAAoB,CAACA,aAAa;MAAC,CAAE;;;;;MAK5D,gBAAgB,CAAC;QAAE4H,OAAO,CAAC,QAAQ,OAAO;MAAC,CAAE;;;;;MAK7CC,SAAS,CAAC;QAAEA,SAAS,CAAC,OAAO,QAAQ;MAAC,CAAE;;;;;;MAMxCC,YAAY,CACR;QACIA,YAAY,CACR,QACA,OACA,IACA,UACA,WACA,UACA,aACAxG,gBAAgB;MAEvB,CAAA;;;;;MAMLyG,UAAU,CAAC;QAAEA,UAAU3F,sBAAqB;OAAI;;;;;MAKhD4F,MAAM,CAAC;QAAEA,MAAM,CAAC,UAAU,MAAM,OAAO,UAAU1G,gBAAgB;OAAG;;;;;MAKpE2G,OAAO,CAAC;QAAEA,OAAO7F,sBAAqB;OAAI;;;;;MAK1C8F,SAAS,CAAC;QAAEA,SAAS,CAAC,QAAQ,QAAQ,QAAQ,SAAS,UAAU5G,gBAAgB;OAAG;;;;;;MAMpF6G,WAAW,CAAC;QAAEA,WAAW,CAAC,IAAI,OAAO,MAAM;OAAG;;;;;MAK9CrH,OAAO,CAAC;QAAEA,OAAO,CAACA,KAAK;MAAC,CAAE;;;;;MAK1B,WAAW,CAAC;QAAE,WAAW,CAACA,KAAK;MAAC,CAAE;;;;;MAKlC,WAAW,CAAC;QAAE,WAAW,CAACA,KAAK;MAAC,CAAE;;;;;MAKlCsH,QAAQ,CAAC;QAAEA,QAAQ,CAACrE,WAAWzC,gBAAgB;MAAC,CAAE;;;;;MAKlD,eAAe,CAAC;QAAE,eAAe,CAACJ,SAAS;MAAC,CAAE;;;;;MAK9C,eAAe,CAAC;QAAE,eAAe,CAACA,SAAS;MAAC,CAAE;;;;;MAK9C,UAAU,CAAC;QAAE,UAAU,CAACF,IAAI;MAAC,CAAE;;;;;MAK/B,UAAU,CAAC;QAAE,UAAU,CAACA,IAAI;MAAC,CAAE;;;;;MAK/B,oBAAoB,CAChB;QACIqH,QAAQ,CACJ,UACA,OACA,aACA,SACA,gBACA,UACA,eACA,QACA,YACA/G,gBAAgB;MAEvB,CAAA;;;;;;MAOLgH,QAAQ,CAAC;QAAEA,QAAQ,CAAC,QAAQ7I,MAAM;MAAC,CAAE;;;;;MAKrC8I,YAAY,CAAC,iBAAiB;;;;;MAK9BC,QAAQ,CACJ;QACIA,QAAQ,CACJ,QACA,WACA,WACA,QACA,QACA,QACA,QACA,eACA,QACA,gBACA,YACA,QACA,aACA,iBACA,SACA,QACA,WACA,QACA,YACA,cACA,cACA,cACA,YACA,YACA,YACA,YACA,aACA,aACA,aACA,aACA,aACA,aACA,eACA,eACA,WACA,YACAlH,gBAAgB;MAEvB,CAAA;;;;;MAML,eAAe,CAAC;QAAEmH,OAAO,CAAChJ,MAAM;MAAC,CAAE;;;;;MAKnC,kBAAkB,CAAC;QAAE,kBAAkB,CAAC,QAAQ,MAAM;MAAC,CAAE;;;;;MAKzDiJ,QAAQ,CAAC;QAAEA,QAAQ,CAAC,QAAQ,KAAK,KAAK,EAAE;OAAG;;;;;MAK3C,mBAAmB,CAAC;QAAEC,QAAQ,CAAC,QAAQ,QAAQ;MAAC,CAAE;;;;;MAKlD,YAAY,CAAC;QAAE,YAAYpH,wBAAuB;OAAI;;;;;MAKtD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,YAAY,CAAC;QAAE,YAAYA,wBAAuB;OAAI;;;;;MAKtD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,aAAa,CAAC;QAAE,aAAaA,wBAAuB;OAAI;;;;;MAKxD,cAAc,CAAC;QAAEqH,MAAM,CAAC,SAAS,OAAO,UAAU,YAAY;OAAG;;;;;MAKjE,aAAa,CAAC;QAAEA,MAAM,CAAC,UAAU,QAAQ;MAAC,CAAE;;;;;MAK5C,aAAa,CAAC;QAAEA,MAAM,CAAC,QAAQ,KAAK,KAAK,MAAM;OAAG;;;;;MAKlD,mBAAmB,CAAC;QAAEA,MAAM,CAAC,aAAa,WAAW;MAAC,CAAE;;;;;MAKxDC,OAAO,CACH;QACIA,OAAO,CACH,QACA,QACA,cACA,gBACA;UAAEC,KAAK,CAAC,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM;SAAG;MAEzD,CAAA;;;;;MAMLC,QAAQ,CAAC;QAAEA,QAAQ,CAAC,QAAQ,QAAQ,OAAO,MAAM;OAAG;;;;;MAKpD,eAAe,CACX;QAAE,eAAe,CAAC,QAAQ,UAAU,YAAY,aAAazH,gBAAgB;MAAG,CAAA;;;;;;MAOpF0H,MAAM,CAAC;QAAEA,MAAM,CAACvJ,QAAQ,MAAM;MAAC,CAAE;;;;;MAKjC,YAAY,CAAC;QAAEwJ,QAAQ,CAACxH,UAAUU,iBAAiB;MAAC,CAAE;;;;;MAKtD8G,QAAQ,CAAC;QAAEA,QAAQ,CAACxJ,QAAQ,MAAM;MAAC,CAAE;;;;;;MAMrCyJ,IAAI,CAAC,WAAW,aAAa;;IAEjCC,wBAAwB;MACpB/F,UAAU,CAAC,cAAc,YAAY;MACrCC,YAAY,CAAC,gBAAgB,cAAc;MAC3C5C,OAAO,CAAC,WAAW,WAAW,SAAS,OAAO,OAAO,SAAS,UAAU,MAAM;MAC9E,WAAW,CAAC,SAAS,MAAM;MAC3B,WAAW,CAAC,OAAO,QAAQ;MAC3BwD,MAAM,CAAC,SAAS,QAAQ,QAAQ;MAChC3D,KAAK,CAAC,SAAS,OAAO;MACtBsE,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;MAClDC,IAAI,CAAC,MAAM,IAAI;MACfC,IAAI,CAAC,MAAM,IAAI;MACfO,GAAG,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;MAClDC,IAAI,CAAC,MAAM,IAAI;MACfC,IAAI,CAAC,MAAM,IAAI;MACf,aAAa,CAAC,SAAS;MACvB,cAAc,CACV,eACA,oBACA,cACA,eACA,cAAc;MAElB,eAAe,CAAC,YAAY;MAC5B,oBAAoB,CAAC,YAAY;MACjC,cAAc,CAAC,YAAY;MAC3B,eAAe,CAAC,YAAY;MAC5B,gBAAgB,CAAC,YAAY;MAC7B6B,SAAS,CACL,aACA,aACA,aACA,aACA,aACA,aACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,YAAY;MAEhB,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,aAAa,CAAC,cAAc,YAAY;MACxC,kBAAkB,CAAC,oBAAoB,kBAAkB;MACzD,YAAY,CACR,cACA,cACA,cACA,cACA,cACA,YAAY;MAEhB,cAAc,CAAC,cAAc,YAAY;MACzC,cAAc,CAAC,cAAc,YAAY;MACzC,gBAAgB,CACZ,kBACA,kBACA,kBACA,gBAAgB;MAEpB,kBAAkB,CAAC,kBAAkB,gBAAgB;MACrD,kBAAkB,CAAC,kBAAkB,gBAAgB;MACrD,YAAY,CACR,aACA,aACA,aACA,aACA,aACA,aACA,aACA,WAAW;MAEf,aAAa,CAAC,aAAa,WAAW;MACtC,aAAa,CAAC,aAAa,WAAW;MACtC,YAAY,CACR,aACA,aACA,aACA,aACA,aACA,aACA,aACA,WAAW;MAEf,aAAa,CAAC,aAAa,WAAW;MACtC,aAAa,CAAC,aAAa,WAAW;;IAE1CgC,gCAAgC;MAC5B,aAAa,CAAC,SAAS;IAC1B;;AAET;;;AChvDgB,SAAAC,aAAaC,YAAoBC,iBAAgC;AAC7E,WAAWC,OAAOD,iBAAiB;AAC/BE,6BAAyBH,YAAmBE,KAAKD,gBAAgBC,GAAmB,CAAC;EACxF;AAED,SAAOF;AACX;AAEA,IAAMI,iBAAiBC,OAAOC,UAAUF;AACxC,IAAMG,gBAAgB,oBAAIC,IAAI,CAAC,UAAU,UAAU,SAAS,CAAC;AAE7D,SAASL,yBACLM,YACAC,UACAC,YAAmB;AAEnB,MACI,CAACP,eAAeQ,KAAKH,YAAYC,QAAQ,KACzCH,cAAcM,IAAI,OAAOF,UAAU,KACnCA,eAAe,MACjB;AACEF,eAAWC,QAAQ,IAAIC;AACvB;EACH;AAED,MAAIG,MAAMC,QAAQJ,UAAU,KAAKG,MAAMC,QAAQN,WAAWC,QAAQ,CAAC,GAAG;AAClED,eAAWC,QAAQ,IAAKD,WAAWC,QAAQ,EAAgBM,OAAOL,UAAU;AAC5E;EACH;AAED,MAAI,OAAOA,eAAe,YAAY,OAAOF,WAAWC,QAAQ,MAAM,UAAU;AAC5E,QAAID,WAAWC,QAAQ,MAAM,MAAM;AAC/BD,iBAAWC,QAAQ,IAAIC;AACvB;IACH;AAED,aAAWM,WAAWN,YAAY;AAC9BR,+BACIM,WAAWC,QAAQ,GACnBO,SACAN,WAAWM,OAAuB,CAAC;IAE1C;EACJ;AACL;;;SC3CgBC,oBACZC,iBACyC;AAAA,WAAA,OAAA,UAAA,QAAtCC,eAAsC,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,CAAA,GAAA,OAAA,GAAA,OAAA,MAAA,QAAA;AAAtCA,iBAAsC,OAAA,CAAA,IAAA,UAAA,IAAA;EAAA;AAEzC,SAAO,OAAOD,oBAAoB,aAC5BE,oBAAmB,MAAA,QAAA,CAACC,kBAAkBH,eAAe,EAAKC,OAAAA,YAAY,CAAC,IACvEC,oBACI,MAAA,QAAA,CAAA,WAAA;AAAA,WAAME,aAAaD,iBAAgB,GAAIH,eAAe;EAAC,CAAA,EAAA,OACpDC,YAAY,CAClB;AACX;;;ICdaI,UAAUC,oBAAoBC,gBAAgB;;;ACYpD,IAAMC,OAAOC;", "names": ["twJoin", "index", "argument", "resolvedValue", "string", "arguments", "length", "toValue", "mix", "k", "CLASS_PART_SEPARATOR", "createClassUtils", "config", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "getClassGroupId", "className", "classParts", "split", "length", "shift", "getGroupRecursive", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "nextPart", "get", "classGroupFromNextClassPart", "slice", "undefined", "validators", "classRest", "join", "find", "validator", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "indexOf", "theme", "prefix", "Map", "prefixedClassGroupEntries", "getPrefixedClassGroupEntries", "Object", "entries", "classGroups", "for<PERSON>ach", "classGroup", "processClassesRecursively", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "push", "key", "path", "currentClassPartObject", "pathPart", "has", "set", "func", "classGroupEntries", "map", "prefixedClassGroup", "fromEntries", "value", "createLruCache", "maxCacheSize", "get", "undefined", "set", "cacheSize", "cache", "Map", "previousCache", "update", "key", "value", "has", "IMPORTANT_MODIFIER", "createSplitModifiers", "config", "separator", "isSeparatorSingleCharacter", "length", "firstSeparatorCharacter", "separator<PERSON><PERSON><PERSON>", "splitModifiers", "className", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "index", "currentCharacter", "slice", "push", "baseClassNameWithImportantModifier", "substring", "hasImportantModifier", "startsWith", "baseClassName", "maybePostfixModifierPosition", "undefined", "sortModifiers", "sortedModifiers", "unsortedModifiers", "for<PERSON>ach", "modifier", "isArbitraryVariant", "sort", "createConfigUtils", "config", "cache", "createLruCache", "cacheSize", "splitModifiers", "createSplitModifiers", "createClassUtils", "SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "splitModifiers", "getClassGroupId", "getConflictingClassGroupIds", "classGroupsInConflict", "Set", "trim", "split", "map", "originalClassName", "modifiers", "hasImportantModifier", "baseClassName", "maybePostfixModifierPosition", "classGroupId", "substring", "hasPostfixModifier", "Boolean", "isTailwindClass", "variantModifier", "sortModifiers", "join", "modifierId", "IMPORTANT_MODIFIER", "reverse", "filter", "parsed", "classId", "has", "add", "for<PERSON>ach", "group", "createTailwindMerge", "createConfig", "configUtils", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "classList", "firstCreateConfig", "restCreateConfig", "config", "reduce", "previousConfig", "createConfigCurrent", "createConfigUtils", "cache", "get", "set", "tailwindMerge", "cachedResult", "result", "mergeClassList", "callTailwindMerge", "twJoin", "apply", "arguments", "fromTheme", "key", "themeGetter", "theme", "isThemeGetter", "arbitraryValueRegex", "fractionRegex", "stringLengths", "Set", "tshirtUnitRegex", "lengthUnitRegex", "shadowRegex", "<PERSON><PERSON><PERSON><PERSON>", "value", "isNumber", "has", "test", "isArbitraryLength", "getIsArbitraryValue", "is<PERSON>engthOnly", "isArbitrarySize", "isNever", "isArbitraryPosition", "isArbitraryUrl", "isUrl", "isArbitraryNumber", "isArbitraryWeight", "Number", "isNaN", "isPercent", "endsWith", "slice", "isInteger", "isIntegerOnly", "isArbitraryValue", "isAny", "isTshirtSize", "isArbitraryShadow", "is<PERSON><PERSON>ow", "label", "testValue", "result", "exec", "startsWith", "getDefaultConfig", "colors", "fromTheme", "spacing", "blur", "brightness", "borderColor", "borderRadius", "borderSpacing", "borderWidth", "contrast", "grayscale", "hueRotate", "invert", "gap", "gradientColorStops", "gradientColorStopPositions", "inset", "margin", "opacity", "padding", "saturate", "scale", "sepia", "skew", "space", "translate", "getOverscroll", "getOverflow", "getSpacingWithAutoAndArbitrary", "isArbitraryValue", "getSpacingWithArbitrary", "getLengthWithEmpty", "<PERSON><PERSON><PERSON><PERSON>", "getNumberWithAutoAndArbitrary", "isNumber", "getPositions", "getLineStyles", "getBlendModes", "getAlign", "getZeroAndEmpty", "getBreaks", "getNumber", "isArbitraryNumber", "getNumberAndArbitrary", "cacheSize", "theme", "isAny", "isTshirtSize", "isPercent", "isArbitraryLength", "classGroups", "aspect", "container", "columns", "box", "display", "clear", "isolation", "object", "overflow", "overscroll", "position", "start", "end", "top", "right", "bottom", "left", "visibility", "z", "isInteger", "basis", "flex", "grow", "shrink", "order", "col", "span", "row", "justify", "content", "items", "self", "p", "px", "py", "ps", "pe", "pt", "pr", "pb", "pl", "m", "mx", "my", "ms", "me", "mt", "mr", "mb", "ml", "w", "screen", "h", "text", "font", "tracking", "leading", "list", "placeholder", "decoration", "indent", "align", "whitespace", "hyphens", "bg", "isArbitraryPosition", "repeat", "isArbitrarySize", "isArbitraryUrl", "from", "via", "to", "rounded", "border", "divide", "outline", "ring", "shadow", "isArbitraryShadow", "filter", "table", "caption", "transition", "duration", "ease", "delay", "animate", "transform", "rotate", "origin", "accent", "appearance", "cursor", "caret", "resize", "scroll", "snap", "touch", "pan", "select", "fill", "stroke", "sr", "conflictingClassGroups", "conflictingClassGroupModifiers", "mergeConfigs", "baseConfig", "configExtension", "key", "mergePropertyRecursively", "hasOwnProperty", "Object", "prototype", "overrideTypes", "Set", "baseObject", "mergeKey", "mergeValue", "call", "has", "Array", "isArray", "concat", "<PERSON><PERSON><PERSON>", "extendTailwindMerge", "configExtension", "createConfig", "createTailwindMerge", "getDefaultConfig", "mergeConfigs", "twMerge", "createTailwindMerge", "getDefaultConfig", "join", "twJoin"]}