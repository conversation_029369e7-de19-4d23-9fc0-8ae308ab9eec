// 基于用户回答生成插画的核心逻辑实现

import { getApiKey } from './apiKeyManager.js';

// 风格描述常量
const STYLE_DESCRIPTION = `温暖友好的儿童插画，使用柔和的色彩和简单清晰的形状，角色表情丰富且易于理解。
水彩画风格，轮廓线条清晰，色彩饱和度适中。使用温暖的棕色、绿色、蓝色和黄色为主。
柔和的光影，避免强烈对比，确保视觉舒适。轻微的水彩纹理，保持整体平滑感。
场景应该简洁不复杂，背景元素适量，主体突出。`;

// 角色描述常量
const CHARACTER_DESCRIPTION = `小熊波波：棕色毛发的小熊，圆脸，大眼睛，友好的表情。
小兔莉莉：灰白色的兔子，长耳朵，温柔的表情。
乌龟老师：绿色的乌龟，戴着眼镜，智慧的表情。
松鼠兄弟：红棕色的松鼠，蓬松的尾巴，活泼的表情。`;

/**
 * 从用户回答中提取关键内容
 * @param {string} answer - 用户的回答内容
 * @returns {Object} 提取的关键内容
 */
function extractKeyContent(answer) {
  // 提取角色
  const characters = ['波波', '小熊', '莉莉', '小兔', '乌龟', '松鼠']
    .filter(char => answer.includes(char));

  // 提取动作和场景
  const actions = [];
  const actionKeywords = ['打招呼', '分享', '帮助', '玩', '说话', '微笑', '拥抱', '交朋友'];
  actionKeywords.forEach(action => {
    if (answer.includes(action)) actions.push(action);
  });

  // 提取情感
  const emotions = [];
  const emotionKeywords = ['开心', '高兴', '害怕', '紧张', '兴奋', '好奇', '担心', '勇敢', '友好'];
  emotionKeywords.forEach(emotion => {
    if (answer.includes(emotion)) emotions.push(emotion);
  });

  // 提取场景元素
  const sceneElements = [];
  const sceneKeywords = ['森林', '树', '花', '草地', '河流', '木屋', '阳光', '雨', '野餐'];
  sceneKeywords.forEach(element => {
    if (answer.includes(element)) sceneElements.push(element);
  });

  return {
    characters: characters.length > 0 ? characters : ['波波', '小熊'],
    actions: actions.length > 0 ? actions : ['微笑'],
    emotions: emotions.length > 0 ? emotions : ['友好'],
    sceneElements: sceneElements.length > 0 ? sceneElements : ['森林']
  };
}

/**
 * 构建图像生成提示词
 * @param {string} answer - 用户的回答内容
 * @param {Object} context - 当前故事上下文
 * @returns {string} 完整的提示词
 */
function buildImagePrompt(answer, context) {
  const keyContent = extractKeyContent(answer);

  // 构建场景描述
  let sceneDescription = `${keyContent.characters.join('和')}在${keyContent.sceneElements.join('和')}中`;

  // 添加动作描述
  if (keyContent.actions.length > 0) {
    sceneDescription += `${keyContent.actions.join('和')}`;
  }

  // 添加情感描述
  if (keyContent.emotions.length > 0) {
    sceneDescription += `，表情${keyContent.emotions.join('和')}`;
  }

  // 结合用户原始回答，添加更详细的风格描述以确保一致性
  let promptBase = `为自闭症儿童绘本创建一幅插图，描绘：${sceneDescription}。
具体情境：${answer}

风格要求：
- 温暖友好的儿童绘本风格
- 柔和的色彩，避免过于鲜艳或刺激的颜色
- 简洁清晰的线条，易于理解
- 卡通风格，但不过于夸张
- 表情温和友善，适合自闭症儿童观看
- 背景简洁，不要过多细节干扰

角色设计：
- 小熊波波：棕色毛发的可爱小熊，圆润的身形，友善的表情
- 其他动物朋友：同样采用温和可爱的设计风格
- 所有角色都应该看起来友好、安全、值得信赖

场景设置：
- 森林环境：绿色为主，有树木、草地、花朵
- 光线柔和，营造温馨的氛围
- 避免阴暗或可能引起恐惧的元素`;

  // 添加上下文相关信息
  if (context && context.currentPage) {
    promptBase += `\n\n当前故事情境：${context.currentPage.content}`;
  }

  return promptBase;
}

/**
 * 获取参考图像URL
 * @param {number} currentPageIndex - 当前页面索引
 * @param {Array} allImages - 所有可用的图像URL
 * @returns {Array} 参考图像URL数组
 */
function getReferenceImages(currentPageIndex, allImages) {
  const references = [];

  // 添加前一页的图像作为参考（如果存在）
  if (currentPageIndex > 0) {
    const prevImage = allImages.find(img => img.pageIndex === currentPageIndex - 1);
    if (prevImage) references.push(prevImage.url);
  }

  // 添加后一页的图像作为参考（如果存在）
  if (currentPageIndex < allImages.length - 1) {
    const nextImage = allImages.find(img => img.pageIndex === currentPageIndex + 1);
    if (nextImage) references.push(nextImage.url);
  }

  // 如果没有找到相邻页面的图像，使用任何可用的图像
  if (references.length === 0 && allImages.length > 0) {
    references.push(allImages[0].url);
  }

  return references;
}

/**
 * 调用OpenAI API生成图像
 * @param {string} prompt - 图像生成提示词
 * @returns {Promise<string>} 生成的图像URL
 */
async function generateImage(prompt) {
  const apiKey = getApiKey();
  if (!apiKey) {
    throw new Error('API密钥未设置');
  }

  try {
    const requestBody = {
      model: "dall-e-3",
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      quality: "standard",
      style: "natural"
    };

    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API错误: ${errorData.error?.message || '未知错误'}`);
    }

    const data = await response.json();
    return data.data[0].url;
  } catch (error) {
    console.error('图像生成失败:', error);
    throw error;
  }
}

/**
 * 保存生成的图像到本地缓存
 * @param {string} imageUrl - 生成的图像URL
 * @param {number} pageId - 页面ID
 * @returns {Promise<string>} 本地图像路径
 */
async function saveGeneratedImage(imageUrl, pageId) {
  try {
    // 在实际应用中，这里会实现将远程图像保存到本地的逻辑
    // 在前端应用中，可以使用localStorage或IndexedDB存储图像URL

    // 模拟保存过程
    console.log(`保存图像: ${imageUrl} 到页面ID: ${pageId}`);

    // 返回图像URL作为本地路径（在实际应用中会返回本地文件路径）
    return imageUrl;
  } catch (error) {
    console.error('保存图像失败:', error);
    throw error;
  }
}

/**
 * 主函数：根据用户回答生成插画
 * @param {string} answer - 用户的回答内容
 * @param {number} pageId - 交互页面ID
 * @param {Object} context - 当前故事上下文
 * @param {Array} allImages - 所有可用的图像（暂时不使用，保留接口兼容性）
 * @returns {Promise<string>} 生成的图像URL或路径
 */
export async function generateIllustrationFromAnswer(answer, pageId, context, allImages) {
  try {
    // 构建提示词
    const prompt = buildImagePrompt(answer, context);

    // 生成图像（DALL-E 3 不支持参考图像，通过提示词保持风格一致性）
    const imageUrl = await generateImage(prompt);

    // 保存图像
    const savedImagePath = await saveGeneratedImage(imageUrl, pageId);

    return savedImagePath;
  } catch (error) {
    console.error('根据回答生成插画失败:', error);
    throw error;
  }
}

/**
 * 检查生成的插画是否与现有风格一致
 * @param {string} generatedImageUrl - 生成的图像URL
 * @param {Array} referenceImages - 参考图像URL数组
 * @returns {Promise<boolean>} 是否风格一致
 */
export async function checkStyleConsistency(generatedImageUrl, referenceImages) {
  // 在实际应用中，这里可以实现风格一致性检查逻辑
  // 可以使用计算机视觉API或简单的颜色分析

  // 模拟检查过程
  console.log('检查风格一致性:', generatedImageUrl, referenceImages);

  // 默认返回一致
  return true;
}

/**
 * 为整套绘本生成插画
 * @param {Array} storyPages - 故事页面数组
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Array>} 生成的插画URL数组
 */
export async function generateStoryIllustrations(storyPages, onProgress) {
  const illustrations = [];
  const nonInteractivePages = storyPages.filter(page => !page.isInteractive);

  console.log(`开始生成 ${nonInteractivePages.length} 张插画...`);

  for (let i = 0; i < nonInteractivePages.length; i++) {
    const page = nonInteractivePages[i];

    try {
      // 报告进度
      if (onProgress) {
        onProgress({
          current: i + 1,
          total: nonInteractivePages.length,
          pageId: page.id,
          status: 'generating'
        });
      }

      // 构建专门的故事插画提示词
      const prompt = buildStoryPagePrompt(page, i, nonInteractivePages.length);

      // 生成图像
      const imageUrl = await generateImage(prompt);

      // 保存结果
      const illustration = {
        pageId: page.id,
        imageUrl: imageUrl,
        prompt: prompt,
        generatedAt: new Date().toISOString()
      };

      illustrations.push(illustration);

      // 报告成功
      if (onProgress) {
        onProgress({
          current: i + 1,
          total: nonInteractivePages.length,
          pageId: page.id,
          status: 'completed',
          imageUrl: imageUrl
        });
      }

      console.log(`页面 ${page.id} 插画生成成功: ${imageUrl}`);

      // 添加延迟以避免API限制
      if (i < nonInteractivePages.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

    } catch (error) {
      console.error(`页面 ${page.id} 插画生成失败:`, error);

      // 报告错误
      if (onProgress) {
        onProgress({
          current: i + 1,
          total: nonInteractivePages.length,
          pageId: page.id,
          status: 'error',
          error: error.message
        });
      }

      // 添加错误记录但继续处理其他页面
      illustrations.push({
        pageId: page.id,
        error: error.message,
        generatedAt: new Date().toISOString()
      });
    }
  }

  return illustrations;
}

/**
 * 构建故事页面专用的图像生成提示词
 * @param {Object} page - 故事页面对象
 * @param {number} pageIndex - 页面在非交互页面中的索引
 * @param {number} totalPages - 总的非交互页面数
 * @returns {string} 完整的提示词
 */
function buildStoryPagePrompt(page, pageIndex, totalPages) {
  // 基础风格描述（确保所有图像风格一致）
  const baseStyle = `
创建一幅温暖友好的儿童绘本插画，专为6-8岁自闭症儿童设计。

风格要求：
- 温暖柔和的水彩风格
- 柔和的色彩搭配：森林绿、温暖棕、天空蓝、花朵粉
- 简洁清晰的线条，避免过多细节
- 圆润可爱的造型设计
- 表情温和友善，营造安全感
- 背景简洁，主体突出

核心角色设计（必须保持一致）：
- 小熊波波：中等身材的棕色小熊，圆润的身形，友善的黑色小眼睛，小小的黑鼻子，穿着简单的红色背心
- 小兔子莉莉：雪白毛发的小兔子，粉红色鼻子，长长的耳朵，穿着淡蓝色小裙子
- 猫头鹰：棕色羽毛，大大的圆眼睛，智慧的表情
- 松鼠兄弟：灰色毛发，蓬松的尾巴，活泼的表情
- 乌龟：绿色的壳，慈祥温和的表情

环境设定：
- 森林场景：绿色为主调，有高大的橡树、松树
- 草地：嫩绿色，点缀着小野花
- 小木屋：温馨的棕色木屋，红色屋顶
- 光线：柔和的自然光，营造温馨氛围`;

  // 根据页面内容生成具体场景描述
  let sceneDescription = '';

  switch (page.id) {
    case 1:
      sceneDescription = `
场景：小熊波波坐在自己的小木屋前
- 波波独自坐在木屋门前的台阶上
- 背景是美丽的森林，远处有大树和野花
- 波波的表情略显孤独但充满好奇
- 早晨的阳光透过树叶洒下斑驳的光影
- 整体氛围宁静而温馨`;
      break;

    case 2:
      sceneDescription = `
场景：波波第一次离开小木屋，沿着小路探索
- 波波正在森林小径上行走，表情好奇而略显紧张
- 小路蜿蜒通向远方，两旁是绿色的树木和灌木
- 可以看到远处传来歌声的方向有微弱的光亮
- 波波的姿态显示出他的勇气和决心
- 森林环境生机勃勃，充满探索的魅力`;
      break;

    case 3:
      sceneDescription = `
场景：波波发现小兔子莉莉在草地上采花
- 开阔的绿色草地，点缀着各色野花
- 莉莉正在采摘花朵，表情愉快，似乎在哼歌
- 波波在远处观察，表情好奇而羞涩
- 阳光明媚，整个场景充满生机和活力
- 花朵种类丰富：雏菊、蒲公英、小野花`;
      break;

    case 5:
      sceneDescription = `
场景：波波和莉莉初次相遇，开始一起采花
- 波波和莉莉并肩站在花丛中
- 两人都在采摘花朵，莉莉在教波波如何选择最美的花
- 表情都很友善和快乐，显示出友谊的开始
- 周围散落着已采摘的花朵
- 背景是美丽的草地和远山`;
      break;

    case 6:
      sceneDescription = `
场景：波波和莉莉一起制作花环，深入交谈
- 两人坐在草地上，手中拿着花朵和半成品花环
- 莉莉正在向波波介绍其他森林朋友
- 表情轻松愉快，显示出友谊的加深
- 周围有制作花环的材料和工具
- 背景可以隐约看到大橡树的轮廓`;
      break;

    case 7:
      sceneDescription = `
场景：波波对参加野餐会既兴奋又紧张
- 波波的表情复杂：既有期待也有担忧
- 莉莉在一旁鼓励他，表情温暖支持
- 背景可以看到通往大橡树的小路
- 夕阳西下，为场景增添温暖的色调
- 整体氛围体现出友谊的支持和鼓励`;
      break;

    case 9:
      sceneDescription = `
场景：野餐会上大家分享美食
- 大橡树下铺着野餐布，上面摆满各种食物
- 猫头鹰、松鼠兄弟、乌龟、莉莉围坐在一起
- 波波站在一旁，表情略显不安
- 食物丰富：蜂蜜饼干、坚果沙拉、新鲜浆果
- 温暖的下午阳光透过树叶洒下`;
      break;

    case 10:
      sceneDescription = `
场景：莉莉安慰波波，传达友谊的真谛
- 莉莉温柔地对波波说话，手轻抚波波的肩膀
- 波波的表情从担忧转为感动和理解
- 其他动物朋友在背景中友善地看着他们
- 整个场景充满温暖和关爱的氛围
- 夕阳的光线为场景增添温馨感`;
      break;

    case 12:
      sceneDescription = `
场景：波波快乐地与所有朋友在一起，展现友谊的美好
- 波波站在朋友们中间，表情自信快乐
- 所有角色都在场：莉莉、猫头鹰、松鼠兄弟、乌龟
- 大家围成一个温馨的圆圈，表情都很开心
- 背景是美丽的森林全景，象征着波波的新世界
- 整体氛围欢乐祥和，体现友谊的力量`;
      break;

    default:
      sceneDescription = `根据故事内容：${page.content}`;
  }

  return `${baseStyle}

${sceneDescription}

故事内容：${page.content}

请确保：
1. 角色外观与之前描述完全一致
2. 色彩风格统一协调
3. 构图简洁明了，适合儿童理解
4. 表情和动作符合故事情节
5. 整体氛围温暖安全，适合自闭症儿童`;
}

export default {
  generateIllustrationFromAnswer,
  generateStoryIllustrations,
  checkStyleConsistency,
  buildImagePrompt,
  buildStoryPagePrompt,
  extractKeyContent
};
